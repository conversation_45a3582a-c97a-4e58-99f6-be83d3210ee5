<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="description" content="أداة متابعة الصفقات الشاملة لتداول العملات الرقمية - منصة احترافية لمراقبة استثماراتك" />
    <title>CryptoTracker Pro V2 - متابعة الصفقات الاحترافية</title>

    <!-- Fonts & Icons -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Tajawal', 'sans-serif'],
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'crypto': {
                            'primary': '#2563eb',
                            'secondary': '#8b5cf6',
                            'accent': '#06b6d4',
                            'success': '#10b981',
                            'danger': '#ef4444',
                            'warning': '#f59e0b',
                            'dark': '#0f172a',
                            'light': '#f8fafc',
                            'gold': '#fbbf24',
                            'silver': '#94a3b8',
                            'bronze': '#d97706'
                        },
                        'glass': {
                            'light': 'rgba(255, 255, 255, 0.1)',
                            'dark': 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.4s ease-out',
                        'slide-down': 'slideDown 0.4s ease-out',
                        'slide-left': 'slideLeft 0.4s ease-out',
                        'slide-right': 'slideRight 0.4s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-gentle': 'bounceGentle 2s infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'float': 'float 3s ease-in-out infinite',
                        'shimmer': 'shimmer 2s linear infinite',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'rotate-slow': 'rotateSlow 8s linear infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    },
                    boxShadow: {
                        'glow': '0 0 20px rgba(59, 130, 246, 0.5)',
                        'glow-lg': '0 0 40px rgba(59, 130, 246, 0.3)',
                        'inner-lg': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.1)',
                        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
                        'neumorphism': '20px 20px 60px #bebebe, -20px -20px 60px #ffffff',
                        'neumorphism-dark': '20px 20px 60px #0a0a0a, -20px -20px 60px #1a1a1a'
                    }
                }
            }
        }
    </script>

    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css" />

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50/40 to-indigo-100/60 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 min-h-screen font-arabic transition-all duration-500 relative overflow-x-hidden">

    <!-- Lightweight Background -->
    <div class="fixed inset-0 pointer-events-none overflow-hidden z-0">
        <!-- Simple Gradient Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/40 to-indigo-50/30 dark:from-slate-900/60 dark:to-slate-800/40"></div>
    </div>

    <!-- ===============================
         HEADER SECTION - شريط العلوي
         =============================== -->
    <header class="sticky top-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-2xl border-b border-slate-200/30 dark:border-slate-700/30 shadow-lg">
        <div class="container mx-auto px-4 py-3 relative">
            <div class="flex items-center justify-between">
                <!-- Empty Left Space -->
                <div class="w-14"></div>

                <!-- Centered Logo & Title -->
                <div class="flex items-center space-x-3 space-x-reverse group">
                    <div class="w-10 h-10 bg-gradient-to-br from-crypto-primary via-crypto-accent to-crypto-secondary rounded-xl flex items-center justify-center shadow-lg shadow-crypto-primary/25">
                        <i class="fas fa-chart-line text-white text-lg"></i>
                    </div>
                    <div class="text-center leading-tight">
                        <h1 class="text-4xl font-bold bg-gradient-to-r from-crypto-primary via-crypto-accent to-crypto-secondary bg-clip-text text-transparent dark:from-blue-400 dark:via-purple-400 dark:to-pink-400 leading-none">
                            CryptoTracker Pro V2
                        </h1>
                        <p class="text-sm text-slate-600 dark:text-slate-200 leading-none mt-1">منصة متابعة الصفقات الاحترافية</p>
                    </div>
                </div>

                <!-- Dark Mode Toggle -->
                <button id="darkModeToggle" onclick="toggleDarkMode()"
                        class="relative w-14 h-7 bg-slate-200 dark:bg-slate-600 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-crypto-primary focus:ring-offset-2 group">
                    <div class="absolute top-0.5 left-0.5 w-6 h-6 bg-white dark:bg-slate-800 rounded-full shadow-md transform transition-transform duration-300 dark:translate-x-7 flex items-center justify-center">
                        <i class="fas fa-sun text-yellow-500 text-xs dark:hidden"></i>
                        <i class="fas fa-moon text-blue-400 text-xs hidden dark:block"></i>
                    </div>
                    <span class="sr-only">تبديل الوضع الليلي</span>
                </button>
            </div>
        </div>
    </header>

    <!-- ===============================
         MAIN CONTENT - المحتوى الرئيسي
         =============================== -->
    <main class="relative z-10 container mx-auto px-4 py-8 space-y-8 max-w-[95%] 2xl:max-w-[90%]">

        <!-- ===============================
             PORTFOLIO OVERVIEW - نظرة عامة على المحفظة
             =============================== -->
        <section class="relative bg-white/90 dark:bg-slate-800/90 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 p-6 overflow-hidden">

            <div class="relative z-10">
                <div class="flex items-center justify-between mb-8">
                    <div class="flex items-center space-x-4 space-x-reverse group">
                        <div class="relative">
                            <div class="w-14 h-14 bg-gradient-to-br from-crypto-accent via-crypto-primary to-crypto-secondary rounded-2xl flex items-center justify-center shadow-lg shadow-crypto-accent/25 group-hover:shadow-xl group-hover:shadow-crypto-accent/40 transition-all duration-300 group-hover:scale-105">
                                <i class="fas fa-chart-pie text-white text-xl group-hover:rotate-12 transition-transform duration-300"></i>
                            </div>
                            <div class="absolute -inset-1 bg-gradient-to-r from-crypto-accent to-crypto-secondary rounded-2xl blur opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                        </div>
                        <div>
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent dark:from-slate-100 dark:to-slate-300">ملخص المحفظة الإجمالي</h2>
                            <p class="text-sm text-slate-600 dark:text-slate-300 font-medium">نظرة شاملة على استثماراتك</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3 space-x-reverse bg-crypto-success/10 dark:bg-crypto-success/20 px-4 py-2 rounded-xl border border-crypto-success/20">
                        <div class="w-3 h-3 bg-crypto-success rounded-full animate-pulse shadow-lg shadow-crypto-success/50"></div>
                        <span class="text-sm text-crypto-success font-semibold">مُحدث تلقائياً</span>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="relative z-10 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Invested Card -->
                <div class="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md border border-blue-200 dark:border-blue-700 hover:shadow-lg transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-wallet text-white text-sm"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-xs text-blue-600 dark:text-blue-400 font-medium">إجمالي المستثمر</p>
                            <p class="text-xl font-bold text-slate-800 dark:text-white font-mono" id="totalInvestedSummary">$0.00</p>
                        </div>
                    </div>
                    <div class="h-1 bg-blue-500 rounded-full"></div>
                </div>

                <!-- Current Value Card -->
                <div class="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md border border-green-200 dark:border-green-700 hover:shadow-lg transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-white text-sm"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-xs text-green-600 dark:text-green-400 font-medium">القيمة الحالية</p>
                            <p class="text-xl font-bold text-slate-800 dark:text-white font-mono" id="totalCurrentValueSummary">$0.00</p>
                        </div>
                    </div>
                    <div class="h-1 bg-green-500 rounded-full"></div>
                </div>

                <!-- P&L Amount Card -->
                <div class="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md border border-purple-200 dark:border-purple-700 hover:shadow-lg transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-hand-holding-dollar text-white text-sm"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-xs text-purple-600 dark:text-purple-400 font-medium">الربح/الخسارة</p>
                            <p class="text-xl font-bold text-slate-800 dark:text-white font-mono" id="totalPnlAmountSummary">$0.00</p>
                        </div>
                    </div>
                    <div class="h-1 bg-purple-500 rounded-full"></div>
                </div>

                <!-- P&L Percentage Card -->
                <div class="bg-white dark:bg-slate-800 rounded-xl p-4 shadow-md border border-orange-200 dark:border-orange-700 hover:shadow-lg transition-shadow duration-200">
                    <div class="flex items-center justify-between mb-3">
                        <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-percent text-white text-sm"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-xs text-orange-600 dark:text-orange-400 font-medium">نسبة الربح</p>
                            <p class="text-xl font-bold text-slate-800 dark:text-white font-mono" id="totalPnlPercentSummary">0.00%</p>
                        </div>
                    </div>
                    <div class="h-1 bg-orange-500 rounded-full"></div>
                </div>
            </div>

            <!-- Portfolio Table -->
            <div class="relative z-10 bg-white/95 dark:bg-slate-800/95 backdrop-blur-xl rounded-2xl shadow-xl border border-slate-200/50 dark:border-slate-600/30 overflow-hidden">
                <!-- Table Header -->
                <div class="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-700 dark:to-slate-600 px-6 py-4 border-b border-slate-200 dark:border-slate-600">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <div class="w-10 h-10 bg-gradient-to-br from-crypto-primary to-crypto-secondary rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-table text-white text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-slate-800 dark:text-slate-100">جدول المحفظة التفصيلي</h3>
                                <p class="text-sm text-slate-600 dark:text-slate-200">عرض شامل لجميع العملات والاستثمارات</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <div class="bg-green-100 dark:bg-green-800 px-3 py-1 rounded-lg border border-green-200 dark:border-green-600">
                                <span class="text-xs text-green-700 dark:text-green-200 font-semibold">مُحدث</span>
                            </div>
                            <button class="w-8 h-8 bg-slate-100 dark:bg-slate-600 hover:bg-slate-200 dark:hover:bg-slate-500 rounded-lg flex items-center justify-center transition-colors">
                                <i class="fas fa-arrows-rotate text-slate-600 dark:text-slate-200 text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Table Container -->
                <div class="overflow-x-auto">
                    <table class="w-full" id="overallSummaryTable">
                        <thead>
                            <tr class="bg-gradient-to-r from-slate-100 to-slate-50 dark:from-slate-600 dark:to-slate-700">
                                <th class="px-4 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-500">
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                        <div class="w-7 h-7 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-coins text-white text-sm"></i>
                                        </div>
                                        <span class="text-sm font-bold">العملة والكمية</span>
                                    </div>
                                </th>
                                <th class="px-4 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-500">
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                        <div class="w-7 h-7 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-dollar-sign text-white text-sm"></i>
                                        </div>
                                        <span class="text-sm font-bold">المستثمر</span>
                                    </div>
                                </th>
                                <th class="px-4 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-500">
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                        <div class="w-7 h-7 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-chart-line text-white text-sm"></i>
                                        </div>
                                        <span class="text-sm font-bold">متوسط الدخول</span>
                                    </div>
                                </th>
                                <th class="px-4 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-500">
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                        <div class="w-7 h-7 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-arrow-trend-up text-white text-sm"></i>
                                        </div>
                                        <span class="text-sm font-bold">السعر الحالي</span>
                                    </div>
                                </th>
                                <th class="px-4 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-500">
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                        <div class="w-7 h-7 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-money-bill-wave text-white text-sm"></i>
                                        </div>
                                        <span class="text-sm font-bold">القيمة الحالية</span>
                                    </div>
                                </th>
                                <th class="px-4 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-500">
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                        <div class="w-7 h-7 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-hand-holding-dollar text-white text-sm"></i>
                                        </div>
                                        <span class="text-sm font-bold">الربح/الخسارة</span>
                                    </div>
                                </th>
                                <th class="px-4 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-500">
                                    <div class="flex items-center justify-center space-x-2 space-x-reverse">
                                        <div class="w-7 h-7 bg-gradient-to-br from-rose-500 to-rose-600 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-percent text-white text-sm"></i>
                                        </div>
                                        <span class="text-sm font-bold">النسبة</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="summaryTableBody" class="bg-white dark:bg-slate-800 divide-y divide-slate-200 dark:divide-slate-600">
                            <tr class="hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors">
                                <td colspan="7" class="px-6 py-12 text-center">
                                    <div class="flex flex-col items-center space-y-4">
                                        <div class="w-16 h-16 bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-600 dark:to-slate-500 rounded-2xl flex items-center justify-center">
                                            <i class="fas fa-chart-line text-2xl text-slate-500 dark:text-slate-300"></i>
                                        </div>
                                        <div class="text-center space-y-2">
                                            <h4 class="text-lg font-bold text-slate-700 dark:text-slate-100">لا توجد بيانات للعرض</h4>
                                            <p class="text-sm text-slate-600 dark:text-slate-200 max-w-md mx-auto">
                                                ابدأ بإضافة عملة رقمية جديدة أو قم بتحديث الأسعار لعرض تفاصيل محفظتك الاستثمارية
                                            </p>
                                            <div class="flex items-center justify-center space-x-3 space-x-reverse mt-4">
                                                <button class="bg-gradient-to-r from-crypto-primary to-crypto-secondary text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all flex items-center space-x-2 space-x-reverse">
                                                    <i class="fas fa-plus text-sm"></i>
                                                    <span class="text-sm font-medium">إضافة عملة</span>
                                                </button>
                                                <button class="bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-200 px-4 py-2 rounded-lg hover:bg-slate-300 dark:hover:bg-slate-500 transition-colors flex items-center space-x-2 space-x-reverse">
                                                    <i class="fas fa-arrows-rotate text-sm"></i>
                                                    <span class="text-sm font-medium">تحديث الأسعار</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- ===============================
             CONTROLS PANEL - لوحة التحكم
             =============================== -->
        <section class="bg-white/90 dark:bg-slate-800/90 rounded-xl shadow-md border border-slate-200 dark:border-slate-700 p-6">
            <div class="flex items-center space-x-3 space-x-reverse mb-6">
                <div class="w-10 h-10 bg-gradient-to-br from-crypto-primary to-crypto-accent rounded-lg flex items-center justify-center">
                    <i class="fas fa-cogs text-white"></i>
                </div>
                <h2 class="text-xl font-bold text-slate-800 dark:text-white">لوحة التحكم</h2>
            </div>

            <!-- Control Panel - Single Row -->
            <div class="flex flex-wrap items-end gap-3 mb-6">
                <!-- Coin Selector -->
                <div class="flex-1 min-w-[200px] space-y-2">
                    <label for="coinSelector" class="block text-sm font-medium text-slate-700 dark:text-slate-300">العملة النشطة</label>
                    <select id="coinSelector" onchange="handleCoinSelectionChange()"
                            class="w-full h-12 px-4 py-3 bg-white dark:bg-slate-900 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-crypto-primary focus:border-transparent transition-all duration-200 text-sm">
                        <option value="">-- اختر عملة --</option>
                    </select>
                </div>

                <!-- Add New Coin -->
                <div class="flex-1 min-w-[200px] space-y-2">
                    <label for="newCoinName" class="block text-sm font-medium text-slate-700 dark:text-slate-300">إضافة عملة جديدة</label>
                    <input type="text" id="newCoinName" placeholder="BTCUSDT"
                           class="w-full h-12 px-4 py-3 bg-white dark:bg-slate-900 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-crypto-primary focus:border-transparent transition-all duration-200 text-sm" />
                </div>

                <!-- Status Display -->
                <div class="flex-1 min-w-[180px] space-y-2">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">حالة النظام</label>
                    <div class="w-full h-12 bg-slate-50 dark:bg-slate-900 border border-slate-300 dark:border-slate-600 rounded-lg px-4 py-2 flex flex-col justify-center">
                        <div id="apiStatus" class="text-xs text-slate-600 dark:text-slate-400 leading-tight">اختر عملة لجلب السعر</div>
                        <div id="coinStatus" class="text-xs text-slate-500 dark:text-slate-500 leading-tight">العملات: 0</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="space-y-2">
                    <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">إجراءات</label>
                    <div class="flex space-x-2 space-x-reverse h-12">
                        <button onclick="addOrSwitchCoin()"
                                class="h-12 bg-gradient-to-r from-crypto-primary to-crypto-secondary text-white px-4 py-3 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-1 space-x-reverse">
                            <i class="fas fa-plus text-sm"></i>
                            <span class="text-sm font-medium">إضافة</span>
                        </button>
                        <button id="refreshPriceBtn" onclick="fetchAllPrices()" title="تحديث كل الأسعار"
                                class="w-12 h-12 bg-gradient-to-r from-crypto-accent to-crypto-secondary text-white rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center justify-center">
                            <i class="fas fa-arrows-rotate text-sm"></i>
                        </button>
                    </div>
                </div>

                <!-- Action Buttons -->
                <input type="file" id="importTxtFile" accept=".txt" style="display:none" onchange="handleImportTxtFile(event)" />

                <button onclick="document.getElementById('importTxtFile').click()"
                        class="h-12 bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-3 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-2 space-x-reverse">
                    <i class="fas fa-file-import text-sm"></i>
                    <span class="text-sm font-medium">استيراد</span>
                </button>

                <button onclick="downloadAllDataAsTxt()"
                        class="h-12 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-3 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-2 space-x-reverse">
                    <i class="fas fa-file-export text-sm"></i>
                    <span class="text-sm font-medium">تصدير</span>
                </button>

                <button id="deleteCoinBtn" onclick="deleteCurrentCoin()" disabled title="حذف العملة المحددة"
                        class="h-12 bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-3 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-2 space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100">
                    <i class="fas fa-trash text-sm"></i>
                    <span class="text-sm font-medium">حذف</span>
                </button>

                <button onclick="deleteAllCoins()" title="حذف جميع العملات من المحفظة"
                        class="h-12 bg-gradient-to-r from-red-600 to-red-700 text-white px-4 py-3 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center justify-center space-x-2 space-x-reverse">
                    <i class="fas fa-trash-alt text-sm"></i>
                    <span class="text-sm font-medium">حذف الكل</span>
                </button>
            </div>
        </section>

        <!-- ===============================
             TRADING DETAILS - تفاصيل التداول
             =============================== -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-8">

            <!-- Trade Entry Section -->
            <section class="bg-white/90 dark:bg-slate-800/90 rounded-xl shadow-md border border-slate-200 dark:border-slate-700 p-6">
                <div class="flex items-center space-x-3 space-x-reverse mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-pen text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-slate-800 dark:text-white">إدخال تفاصيل الصفقة</h2>
                        <p class="text-sm text-slate-600 dark:text-slate-400">العملة: <span id="currentCoinDisplay1" class="font-medium text-crypto-primary">---</span></p>
                    </div>
                </div>

                <div class="space-y-6">
                    <!-- Entry Price -->
                    <div class="space-y-2">
                        <label for="initialEntryPrice" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            <i class="fas fa-chart-line text-crypto-primary mr-2"></i>سعر الدخول الأولي
                        </label>
                        <input type="number" id="initialEntryPrice" step="any" placeholder="0.00" oninput="saveAndCalculate()"
                               class="w-full px-4 py-3 bg-white dark:bg-slate-900 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-crypto-primary focus:border-transparent transition-all duration-200 font-mono text-lg" />
                    </div>

                    <!-- Investment Amount -->
                    <div class="space-y-2">
                        <label for="initialAmountDollars" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            <i class="fas fa-dollar-sign text-crypto-primary mr-2"></i>المبلغ بالدولار
                        </label>
                        <input type="number" id="initialAmountDollars" step="any" placeholder="0.00" oninput="saveAndCalculate()"
                               class="w-full px-4 py-3 bg-white dark:bg-slate-900 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-crypto-primary focus:border-transparent transition-all duration-200 font-mono text-lg" />
                    </div>

                    <!-- Calculated Quantity -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            <i class="fas fa-coins text-crypto-primary mr-2"></i>كمية العملة المحصلة
                        </label>
                        <div id="initialCoinQty" class="w-full px-4 py-3 bg-slate-50 dark:bg-slate-900 border border-slate-300 dark:border-slate-600 rounded-lg font-mono text-lg text-slate-800 dark:text-slate-200">0.00</div>
                    </div>

                    <!-- Current Market Price -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
                            <i class="fas fa-arrow-trend-up text-crypto-primary mr-2"></i>السعر الحالي
                        </label>
                        <div id="marketPriceDisplay" class="w-full px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 border border-blue-200 dark:border-slate-600 rounded-lg font-mono text-lg font-bold text-crypto-primary">---</div>
                    </div>

                    <!-- Auto Refresh -->
                    <div class="flex items-center space-x-3 space-x-reverse p-4 bg-slate-50 dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700">
                        <input type="checkbox" id="autoRefreshCheckbox" class="w-5 h-5 text-crypto-primary bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 rounded focus:ring-crypto-primary focus:ring-2" />
                        <label for="autoRefreshCheckbox" class="text-sm text-slate-700 dark:text-slate-300 flex items-center space-x-2 space-x-reverse">
                            <i class="fas fa-sync-alt text-crypto-primary"></i>
                            <span>تحديث تلقائي للأسعار (كل 30 ثانية)</span>
                        </label>
                    </div>
                </div>
            </section>

            <!-- Current Status & Targets -->
            <section class="bg-white/90 dark:bg-slate-800/90 rounded-xl shadow-md border border-slate-200 dark:border-slate-700 p-6">
                <div class="flex items-center space-x-3 space-x-reverse mb-6">
                    <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-target text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-slate-800 dark:text-white">الوضع الحالي والأهداف</h2>
                        <p class="text-sm text-slate-600 dark:text-slate-400">العملة: <span id="currentCoinDisplay3" class="font-medium text-crypto-primary">---</span></p>
                    </div>
                </div>

                <!-- Current Status Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-4 border border-blue-200 dark:border-blue-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-blue-600 dark:text-blue-400 font-medium">إجمالي الكمية</p>
                                <p id="totalCoinQty" class="text-lg font-bold text-blue-800 dark:text-blue-200 font-mono">0.00</p>
                            </div>
                            <i class="fas fa-coins text-blue-500 text-xl"></i>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-4 border border-green-200 dark:border-green-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-green-600 dark:text-green-400 font-medium">إجمالي المستثمر</p>
                                <p id="totalInvestedAmount" class="text-lg font-bold text-green-800 dark:text-green-200 font-mono">$0.00</p>
                            </div>
                            <i class="fas fa-wallet text-green-500 text-xl"></i>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-4 border border-purple-200 dark:border-purple-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-purple-600 dark:text-purple-400 font-medium">متوسط الدخول</p>
                                <p id="averageEntryPrice" class="text-lg font-bold text-purple-800 dark:text-purple-200 font-mono">$0.00</p>
                            </div>
                            <i class="fas fa-chart-line text-purple-500 text-xl"></i>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 rounded-xl p-4 border border-indigo-200 dark:border-indigo-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-indigo-600 dark:text-indigo-400 font-medium">القيمة الحالية</p>
                                <p id="currentPortfolioValue" class="text-lg font-bold text-indigo-800 dark:text-indigo-200 font-mono">$0.00</p>
                            </div>
                            <i class="fas fa-money-bill-wave text-indigo-500 text-xl"></i>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 rounded-xl p-4 border border-emerald-200 dark:border-emerald-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-emerald-600 dark:text-emerald-400 font-medium">الربح/الخسارة</p>
                                <p id="pnlAmount" class="text-lg font-bold text-emerald-800 dark:text-emerald-200 font-mono pnl-neutral">$0.00</p>
                            </div>
                            <i class="fas fa-hand-holding-dollar text-emerald-500 text-xl"></i>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-900/20 dark:to-cyan-800/20 rounded-xl p-4 border border-cyan-200 dark:border-cyan-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-cyan-600 dark:text-cyan-400 font-medium">الكمية المتبقية</p>
                                <p id="remainingQuantityDisplay" class="text-2xl font-bold text-cyan-800 dark:text-cyan-200 font-mono">0.00</p>
                            </div>
                            <i class="fas fa-coins text-cyan-500 text-xl"></i>
                        </div>
                    </div>

                    <div class="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 rounded-xl p-4 border border-amber-200 dark:border-amber-700">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-xs text-amber-600 dark:text-amber-400 font-medium">نسبة الربح</p>
                                <p id="pnlPercent" class="text-lg font-bold text-amber-800 dark:text-amber-200 font-mono pnl-neutral">0.00%</p>
                            </div>
                            <i class="fas fa-percent text-amber-500 text-xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Targets Section -->
                <div class="border-t border-slate-200 dark:border-slate-700 pt-6">
                    <div class="flex items-center space-x-2 space-x-reverse mb-6">
                        <i class="fas fa-bullseye text-crypto-primary"></i>
                        <h3 class="text-lg font-bold text-slate-800 dark:text-white">الأهداف ووقف الخسارة</h3>
                        <span class="text-sm text-slate-500 dark:text-slate-400">(من متوسط الدخول)</span>
                    </div>

                    <div class="space-y-6">
                        <!-- Take Profit Targets -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Target 1 -->
                            <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-4 border border-green-200 dark:border-green-700">
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-flag text-green-600"></i>
                                        <label for="tpPercent1" class="text-sm font-medium text-green-700 dark:text-green-300">هدف 1 (%)</label>
                                    </div>
                                    <input type="number" id="tpPercent1" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"
                                           class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-green-300 dark:border-green-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 font-mono" />
                                    <div class="space-y-1">
                                        <label class="text-xs text-green-600 dark:text-green-400">سعر الخروج</label>
                                        <div id="tpPrice1" class="px-3 py-2 bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg font-mono text-green-800 dark:text-green-200 font-bold">$0.00</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Target 2 -->
                            <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-700">
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-flag text-blue-600"></i>
                                        <label for="tpPercent2" class="text-sm font-medium text-blue-700 dark:text-blue-300">هدف 2 (%)</label>
                                    </div>
                                    <input type="number" id="tpPercent2" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"
                                           class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-blue-300 dark:border-blue-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 font-mono" />
                                    <div class="space-y-1">
                                        <label class="text-xs text-blue-600 dark:text-blue-400">سعر الخروج</label>
                                        <div id="tpPrice2" class="px-3 py-2 bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg font-mono text-blue-800 dark:text-blue-200 font-bold">$0.00</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Target 3 -->
                            <div class="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-4 border border-purple-200 dark:border-purple-700">
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-flag text-purple-600"></i>
                                        <label for="tpPercent3" class="text-sm font-medium text-purple-700 dark:text-purple-300">هدف 3 (%)</label>
                                    </div>
                                    <input type="number" id="tpPercent3" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"
                                           class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-purple-300 dark:border-purple-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 font-mono" />
                                    <div class="space-y-1">
                                        <label class="text-xs text-purple-600 dark:text-purple-400">سعر الخروج</label>
                                        <div id="tpPrice3" class="px-3 py-2 bg-purple-100 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700 rounded-lg font-mono text-purple-800 dark:text-purple-200 font-bold">$0.00</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Stop Loss -->
                        <div class="bg-gradient-to-br from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 rounded-xl p-4 border border-red-200 dark:border-red-700">
                            <div class="grid grid-cols-2 gap-4 items-end">
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <i class="fas fa-shield-alt text-red-600"></i>
                                        <label for="slPercent" class="text-sm font-medium text-red-700 dark:text-red-300">وقف الخسارة (%)</label>
                                    </div>
                                    <input type="number" id="slPercent" step="any" min="0" placeholder="%" oninput="saveAndCalculate()"
                                           class="w-full px-3 py-2 bg-white dark:bg-slate-900 border border-red-300 dark:border-red-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 font-mono" />
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-2 space-x-reverse h-6">
                                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                                        <label class="text-sm font-medium text-red-700 dark:text-red-300">سعر وقف الخسارة</label>
                                    </div>
                                    <div id="slPrice" class="px-3 py-2 bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg font-mono text-red-800 dark:text-red-200 font-bold">$0.00</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- ===============================
             DCA SECTION - قسم التعزيز
             =============================== -->
        <section class="bg-white/90 dark:bg-slate-800/90 rounded-xl shadow-md border border-slate-200 dark:border-slate-700 p-6">
            <!-- Header Section -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 via-purple-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                        <i class="fas fa-layer-group text-white text-lg"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">التعزيز (DCA)</h2>
                        <p class="text-sm text-slate-600 dark:text-slate-400">العملة: <span id="currentCoinDisplay2" class="font-medium text-crypto-primary">---</span></p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button onclick="addDCARow()" class="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-plus text-sm"></i>
                        <span class="text-sm font-medium">إضافة صف</span>
                    </button>
                    <button onclick="clearAllDCA()" class="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center space-x-2 space-x-reverse">
                        <i class="fas fa-trash text-sm"></i>
                        <span class="text-sm font-medium">مسح الكل</span>
                    </button>
                </div>
            </div>

            <!-- DCA Table Container -->
            <div class="bg-white dark:bg-slate-900 rounded-xl shadow-lg overflow-hidden border border-slate-200 dark:border-slate-700">
                <div class="overflow-x-auto">
                    <table class="w-full dca-table" id="dcaTable">
                        <thead class="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700">
                            <tr>
                                <!-- رقم الصف -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-hashtag text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">#</span>
                                    </div>
                                </th>

                                <!-- نسبة الهبوط -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-red-500 to-red-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-arrow-down text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">هبوط %</span>
                                    </div>
                                </th>

                                <!-- سعر التعزيز -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-purple-500 to-purple-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-chart-line text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">سعر التعزيز</span>
                                    </div>
                                </th>

                                <!-- المبلغ -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-dollar-sign text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">المبلغ $</span>
                                    </div>
                                </th>

                                <!-- الكمية -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-coins text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">الكمية</span>
                                    </div>
                                </th>

                                <!-- الربح/الخسارة الحالية -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-amber-500 to-amber-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-hand-holding-dollar text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">ربح/خسارة حالي</span>
                                    </div>
                                </th>

                                <!-- سعر البيع المستهدف -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-bullseye text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">سعر البيع المستهدف</span>
                                    </div>
                                </th>

                                <!-- الربح المتوقع -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-teal-500 to-teal-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-chart-bar text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">الربح المتوقع</span>
                                    </div>
                                </th>

                                <!-- إجراءات -->
                                <th class="px-3 py-4 text-center font-bold text-slate-800 dark:text-slate-100 border-b-2 border-slate-200 dark:border-slate-600">
                                    <div class="flex items-center justify-center space-x-1 space-x-reverse">
                                        <div class="w-6 h-6 bg-gradient-to-br from-slate-500 to-slate-600 rounded-md flex items-center justify-center">
                                            <i class="fas fa-cog text-white text-xs"></i>
                                        </div>
                                        <span class="text-xs font-bold">بيع</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="repurchaseRows" class="bg-white dark:bg-slate-900 divide-y divide-slate-200 dark:divide-slate-700">
                            <!-- DCA rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- Empty State -->
                <div id="dcaEmptyState" class="text-center py-12 px-6" style="display: none;">
                    <div class="flex flex-col items-center space-y-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-layer-group text-2xl text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="text-center space-y-2">
                            <h4 class="text-lg font-bold text-slate-700 dark:text-slate-200">لا توجد صفوف تعزيز</h4>
                            <p class="text-sm text-slate-600 dark:text-slate-400 max-w-md mx-auto">
                                ابدأ بإضافة صفوف التعزيز لتخطيط استراتيجية DCA الخاصة بك
                            </p>
                            <button onclick="addDCARow()" class="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 flex items-center space-x-2 space-x-reverse mx-auto mt-4">
                                <i class="fas fa-plus text-sm"></i>
                                <span class="text-sm font-medium">إضافة أول صف تعزيز</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- ===============================
         TRADING CHART SECTION - قسم الشارت
         =============================== -->
    <section class="container mx-auto px-4 py-8 max-w-7xl">
        <div class="bg-white/90 dark:bg-slate-800/90 rounded-xl shadow-md border border-slate-200 dark:border-slate-700 p-6">
            <div class="flex items-center space-x-3 space-x-reverse mb-6">
                <div class="w-10 h-10 bg-gradient-to-br from-crypto-accent to-crypto-primary rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-candlestick text-white"></i>
                </div>
                <div>
                    <h2 class="text-2xl font-bold text-slate-800 dark:text-white">شارت العملة التفاعلي</h2>
                    <p class="text-sm text-slate-600 dark:text-slate-400">مدعوم بـ TradingView</p>
                </div>
            </div>

            <div class="bg-white dark:bg-slate-900 rounded-xl shadow-lg overflow-hidden border border-slate-200 dark:border-slate-700">
                <div class="tradingview-widget-container">
                    <div id="tradingview_cryptochart" style="direction:ltr; min-height: 400px;">
                        <div class="flex items-center justify-center h-full text-slate-500 dark:text-slate-400">
                            <div class="text-center">
                                <i class="fas fa-chart-line text-4xl mb-4 text-crypto-primary"></i>
                                <p class="text-lg font-medium">اختر عملة لعرض الشارت</p>
                                <p class="text-sm mt-2">سيتم تحميل شارت TradingView التفاعلي</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ===============================
         FOOTER SECTION - تذييل الصفحة
         =============================== -->
    <footer class="relative z-10 bg-slate-900 text-white py-8 mt-16">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Developer Info -->
                <div class="text-center md:text-right">
                    <h3 class="text-lg font-bold mb-4 text-crypto-accent">المطور</h3>
                    <p class="text-slate-300 mb-2">المهندس معتز</p>
                    <a href="https://t.me/TadawulGY" target="_blank"
                       class="inline-flex items-center space-x-2 space-x-reverse bg-gradient-to-r from-crypto-primary to-crypto-secondary text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105">
                        <i class="fab fa-telegram"></i>
                        <span>@TadawulGy</span>
                    </a>
                </div>

                <!-- App Info -->
                <div class="text-center">
                    <h3 class="text-lg font-bold mb-4 text-crypto-accent">CryptoTracker Pro</h3>
                    <p class="text-slate-300 mb-4">منصة احترافية لمتابعة صفقات العملات الرقمية</p>
                    <div class="flex justify-center space-x-4 space-x-reverse">
                        <span class="text-sm font-semibold text-green-400 bg-green-500/10 px-3 py-1 rounded-full border border-green-500/20 hover:bg-green-500/20 hover:text-green-300 hover:border-green-400/40 transition-all duration-300 cursor-pointer hover:scale-105">
                            <i class="fas fa-gift mr-1"></i>مجاني
                        </span>
                        <span class="text-sm font-semibold text-blue-400 bg-blue-500/10 px-3 py-1 rounded-full border border-blue-500/20 hover:bg-blue-500/20 hover:text-blue-300 hover:border-blue-400/40 transition-all duration-300 cursor-pointer hover:scale-105">
                            <i class="fas fa-shield-alt mr-1"></i>آمن
                        </span>
                        <span class="text-sm font-semibold text-purple-400 bg-purple-500/10 px-3 py-1 rounded-full border border-purple-500/20 hover:bg-purple-500/20 hover:text-purple-300 hover:border-purple-400/40 transition-all duration-300 cursor-pointer hover:scale-105">
                            <i class="fas fa-bolt mr-1"></i>سريع
                        </span>
                    </div>
                </div>

                <!-- Prayer Request -->
                <div class="text-center md:text-left">
                    <h3 class="text-lg font-bold mb-4 text-crypto-accent">دعواتكم</h3>
                    <p class="text-slate-300 text-sm leading-relaxed">
                        نسألكم الدعاء بظهر الغيب<br>
                        جزاكم الله خيراً
                    </p>
                </div>
            </div>

            <div class="border-t border-slate-700 mt-8 pt-6 text-center">
                <p class="text-slate-400 text-sm">
                    © 2024 CryptoTracker Pro - جميع الحقوق محفوظة
                </p>
            </div>
        </div>
    </footer>

    <!-- ===============================
         MODAL DIALOG - نافذة منبثقة
         =============================== -->
    <div id="customModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4" style="display:none;">
        <div class="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden border border-slate-200 dark:border-slate-700">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-crypto-primary to-crypto-secondary text-white px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 id="customModalTitle" class="text-lg font-bold">تنبيه</h3>
                    <button class="text-white hover:text-slate-200 transition-colors duration-200" onclick="closeCustomModal()">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div id="customModalBody" class="px-6 py-6 text-slate-800 dark:text-slate-200">
                <!-- Content will be inserted here -->
            </div>

            <!-- Modal Footer -->
            <div class="px-6 py-4 bg-slate-50 dark:bg-slate-900 flex justify-end space-x-3 space-x-reverse">
                <button id="customModalOkBtn"
                        class="bg-gradient-to-r from-crypto-primary to-crypto-secondary text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 font-medium">
                    موافق
                </button>
                <button id="customModalCancelBtn"
                        class="bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-200 hover:scale-105 font-medium"
                        style="display:none;">
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- ===============================
         SCRIPTS - الملفات البرمجية
         =============================== -->
    <!-- TradingView Widget Script -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script>
        const TV_SELECTED_COIN_KEY = "tv_selected_coin";

        function loadTradingViewChart(symbol) {
            const chartContainer = document.getElementById('tradingview_cryptochart');
            if (!chartContainer) {
                console.warn('TradingView chart container not found');
                return;
            }

            if (!symbol || !window.TradingView) {
                // عرض رسالة افتراضية
                chartContainer.innerHTML = `
                    <div class="flex items-center justify-center h-full text-slate-500 dark:text-slate-400" style="min-height: 400px;">
                        <div class="text-center">
                            <i class="fas fa-chart-line text-4xl mb-4 text-crypto-primary"></i>
                            <p class="text-lg font-medium">اختر عملة لعرض الشارت</p>
                            <p class="text-sm mt-2">سيتم تحميل شارت TradingView التفاعلي</p>
                        </div>
                    </div>
                `;
                return;
            }

            // عرض رسالة تحميل
            chartContainer.innerHTML = `
                <div class="flex items-center justify-center h-full text-slate-500 dark:text-slate-400" style="min-height: 400px;">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-crypto-primary mx-auto mb-4"></div>
                        <p class="text-lg font-medium">جاري تحميل شارت ${symbol}</p>
                        <p class="text-sm mt-2">يرجى الانتظار...</p>
                    </div>
                </div>
            `;

            let chartHeight = 450;

            // تعديل ارتفاع الشارت بناءً على حجم الشاشة
            if (window.innerWidth <= 768) {
                chartHeight = 350;
            }
            if (window.innerWidth <= 500) {
                chartHeight = 300;
            }
            if (window.innerWidth <= 375) {
                chartHeight = 250;
            }

            try {
                setTimeout(() => {
                    chartContainer.innerHTML = "";

                    // تحديد الثيم بناءً على الوضع الحالي
                    const isDarkMode = document.documentElement.classList.contains('dark');
                    const theme = isDarkMode ? "dark" : "light";
                    const toolbarBg = isDarkMode ? "#1e293b" : "#f1f3f6";

                    new TradingView.widget({
                        "width": "100%",
                        "height": chartHeight,
                        "symbol": "BINANCE:" + symbol,
                        "interval": "30",
                        "timezone": "Etc/UTC",
                        "theme": theme,
                        "style": "1",
                        "locale": "ar_AE",
                        "toolbar_bg": toolbarBg,
                        "enable_publishing": false,
                        "allow_symbol_change": false,
                        "container_id": "tradingview_cryptochart",
                        "hide_side_toolbar": false,
                        "studies": [],
                        "show_popup_button": true,
                        "popup_width": "1000",
                        "popup_height": "650"
                    });

                    // حفظ العملة المختارة في localStorage
                    localStorage.setItem(TV_SELECTED_COIN_KEY, symbol);
                }, 500);
            } catch (error) {
                console.error('Error loading TradingView chart:', error);
                chartContainer.innerHTML = `
                    <div class="flex items-center justify-center h-full text-red-500" style="min-height: 400px;">
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                            <p class="text-lg font-medium">خطأ في تحميل الشارت</p>
                            <p class="text-sm mt-2">يرجى المحاولة مرة أخرى</p>
                        </div>
                    </div>
                `;
            }
        }

        // تحديث الشارت عند تغيير العملة أو الوضع الليلي
        function updateTradingViewChart() {
            if (activeCoinSymbol && window.TradingView) {
                setTimeout(() => {
                    loadTradingViewChart(activeCoinSymbol);
                }, 500);
            }
        }

        // دالة لتحديث ثيم الشارت فقط
        function updateChartTheme() {
            if (activeCoinSymbol && window.TradingView) {
                console.log('Updating chart theme for:', activeCoinSymbol);
                // إعادة تحميل الشارت بالثيم الجديد
                loadTradingViewChart(activeCoinSymbol);
            }
        }

        // دالة للتأكد من تحديث الشارت عند تحميل الصفحة
        function initializeChartWithTheme() {
            if (activeCoinSymbol && window.TradingView) {
                setTimeout(() => {
                    loadTradingViewChart(activeCoinSymbol);
                }, 1000);
            }
        }
    </script>

    <script src="script.js"></script>

    <!-- Dark Mode Script -->
    <script>
        // Dark Mode Functionality
        function initDarkMode() {
            // Check for saved theme preference or default to light mode
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }

        function toggleDarkMode() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');

            if (isDark) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }

            // Update TradingView chart theme if it exists
            if (typeof updateChartTheme === 'function') {
                setTimeout(updateChartTheme, 200);
            } else if (typeof updateTradingViewChart === 'function') {
                setTimeout(updateTradingViewChart, 200);
            }
        }

        // Initialize dark mode on page load
        document.addEventListener('DOMContentLoaded', function() {
            initDarkMode();

            // تحديث الشارت بعد تحميل الصفحة
            setTimeout(() => {
                if (typeof initializeChartWithTheme === 'function') {
                    initializeChartWithTheme();
                }
            }, 2000);
        });

        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
            if (!localStorage.getItem('theme')) {
                if (e.matches) {
                    document.documentElement.classList.add('dark');
                } else {
                    document.documentElement.classList.remove('dark');
                }

                // Update chart theme when system theme changes
                if (typeof updateChartTheme === 'function') {
                    setTimeout(updateChartTheme, 300);
                }
            }
        });
    </script>

    <!-- Custom Animations -->
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fade-in {
            animation: fadeIn 0.3s ease-out forwards;
        }

        .animate-slide-up {
            animation: slideUp 0.2s ease-out forwards;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
        }

        /* Dark mode scrollbar */
        .dark ::-webkit-scrollbar-track {
            background: #1e293b;
        }

        .dark ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #475569, #334155);
        }

        .dark ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #64748b, #475569);
        }

        /* تحسين وضوح النصوص والأرقام */
        .font-mono {
            font-family: 'Courier New', Courier, monospace;
            font-weight: 600;
        }

        /* تحسين التباين للأرقام المهمة */
        .text-2xl.font-bold {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .dark .text-2xl.font-bold {
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
        }

        /* تحسين خلفية البطاقات */
        .bg-white {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .dark .bg-slate-800 {
            background-color: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(10px);
        }

        /* تحسين وضوح الأرقام في البطاقات */
        .summary-card-number {
            font-size: 1.75rem;
            font-weight: 800;
            letter-spacing: -0.025em;
            line-height: 1.2;
        }

        /* تحسين التباين للنصوص */
        .text-slate-800 {
            color: #1e293b !important;
        }

        .dark .text-white {
            color: #ffffff !important;
        }

        /* تحسين الحدود الملونة */
        .border-l-4 {
            border-left-width: 5px;
        }

        /* تحسين وضوح الأرقام في البطاقات فقط */
        .summary-card-number {
            color: #1e293b !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .dark .summary-card-number {
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
        }

        /* تحسين وضوح الأرقام في الجداول فقط */
        table .font-mono {
            font-weight: 600;
            letter-spacing: 0.025em;
        }

        /* تحسين عناوين الجداول فقط */
        table th {
            font-weight: 600;
            position: relative;
        }

        /* ===========================
           PORTFOLIO TABLE STYLES - REBUILT
           =========================== */
        #overallSummaryTable {
            width: 100%;
            border-collapse: collapse;
            table-layout: auto;
            min-width: 800px;
            background-color: #ffffff;
        }

        /* تحديد عرض الأعمدة بشكل متوازن */
        #overallSummaryTable th:nth-child(1) { width: 20%; } /* العملة والكمية */
        #overallSummaryTable th:nth-child(2) { width: 15%; } /* المستثمر */
        #overallSummaryTable th:nth-child(3) { width: 15%; } /* متوسط الدخول */
        #overallSummaryTable th:nth-child(4) { width: 15%; } /* السعر الحالي */
        #overallSummaryTable th:nth-child(5) { width: 15%; } /* القيمة الحالية */
        #overallSummaryTable th:nth-child(6) { width: 10%; } /* الربح/الخسارة */
        #overallSummaryTable th:nth-child(7) { width: 10%; } /* النسبة */

        /* رؤوس الأعمدة */
        #overallSummaryTable th {
            padding: 16px 12px;
            text-align: center;
            font-weight: 700;
            font-size: 0.875rem;
            color: #1e293b;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 2px solid #e2e8f0;
            position: relative;
            transition: all 0.2s ease;
        }

        #overallSummaryTable th:hover {
            background: #f1f5f9;
        }

        /* خلايا البيانات */
        #overallSummaryTable td {
            padding: 14px 12px;
            text-align: center;
            font-size: 0.875rem;
            color: #374151;
            background-color: #ffffff;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
            transition: all 0.2s ease;
        }

        /* تأثيرات الصفوف */
        #overallSummaryTable tbody tr {
            transition: all 0.2s ease;
        }

        #overallSummaryTable tbody tr:hover {
            background-color: #f8fafc;
        }

        #overallSummaryTable tbody tr:nth-child(even) {
            background-color: #fafbfc;
        }

        #overallSummaryTable tbody tr:nth-child(even):hover {
            background-color: #f1f5f9;
        }

        /* تحسين الخطوط */
        #overallSummaryTable .font-mono {
            font-family: 'Inter', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-weight: 600;
            font-size: 0.875rem;
        }

        /* تحسين العمود الأول */
        #overallSummaryTable td:first-child {
            font-weight: 600;
        }

        /* تحسين الأيقونات */
        #overallSummaryTable th .w-7 {
            transition: none;
        }

        /* تحسين الألوان للوضع الليلي */
        .dark #overallSummaryTable {
            background-color: #1e293b;
        }

        .dark #overallSummaryTable th {
            color: #f1f5f9;
            background: linear-gradient(135deg, #334155 0%, #475569 100%);
            border-bottom-color: #64748b;
        }

        .dark #overallSummaryTable th:hover {
            background: linear-gradient(135deg, #475569 0%, #64748b 100%);
        }

        .dark #overallSummaryTable td {
            color: #e2e8f0;
            background-color: #1e293b;
            border-bottom-color: #334155;
        }

        .dark #overallSummaryTable tbody tr:hover {
            background-color: #334155;
        }

        /* ===========================
           DCA TABLE STYLES - ENHANCED
           =========================== */
        .dca-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: auto;
            min-width: 1100px;
            background-color: #ffffff;
        }

        /* تحديد عرض الأعمدة بشكل متوازن */
        .dca-table th:nth-child(1) { width: 6%; }   /* # */
        .dca-table th:nth-child(2) { width: 10%; }  /* هبوط % */
        .dca-table th:nth-child(3) { width: 14%; }  /* سعر التعزيز */
        .dca-table th:nth-child(4) { width: 12%; }  /* المبلغ $ */
        .dca-table th:nth-child(5) { width: 14%; }  /* الكمية */
        .dca-table th:nth-child(6) { width: 14%; }  /* ربح/خسارة حالي */
        .dca-table th:nth-child(7) { width: 16%; }  /* سعر البيع المستهدف */
        .dca-table th:nth-child(8) { width: 16%; }  /* الربح المتوقع */
        .dca-table th:nth-child(9) { width: 8%; }   /* إجراءات */

        /* رؤوس الأعمدة */
        .dca-table th {
            padding: 12px 8px;
            text-align: center;
            font-weight: 700;
            font-size: 0.75rem;
            color: #1e293b;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-bottom: 2px solid #e2e8f0;
            position: relative;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .dca-table th:hover {
            background: #f1f5f9;
        }

        /* خلايا البيانات */
        .dca-table td {
            padding: 10px 6px;
            text-align: center;
            font-size: 0.75rem;
            color: #374151;
            background-color: #ffffff;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
            transition: all 0.2s ease;
        }

        /* تأثيرات الصفوف */
        .dca-table tbody tr {
            transition: all 0.2s ease;
        }

        .dca-table tbody tr:hover {
            background-color: #f8fafc;
        }

        .dca-table tbody tr:nth-child(even) {
            background-color: #fafbfc;
        }

        .dca-table tbody tr:nth-child(even):hover {
            background-color: #f1f5f9;
        }

        /* تحسين الخطوط */
        .dca-table .font-mono {
            font-family: 'Inter', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-weight: 600;
            font-size: 0.75rem;
        }

        /* تحسين الأيقونات */
        .dca-table th .w-6 {
            transition: none;
        }

        /* تحسين الألوان للوضع الليلي */
        .dark .dca-table {
            background-color: #1e293b;
        }

        .dark .dca-table th {
            color: #f1f5f9;
            background: linear-gradient(135deg, #334155 0%, #475569 100%);
            border-bottom-color: #64748b;
        }

        .dark .dca-table th:hover {
            background: linear-gradient(135deg, #475569 0%, #64748b 100%);
        }

        .dark .dca-table td {
            color: #e2e8f0;
            background-color: #1e293b;
            border-bottom-color: #334155;
        }

        .dark .dca-table tbody tr:hover {
            background-color: #334155;
        }

        .dark .dca-table tbody tr:nth-child(even) {
            background-color: #2d3748;
        }

        .dark .dca-table tbody tr:nth-child(even):hover {
            background-color: #334155;
        }

        /* DCA Input Fields */
        .dca-input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.75rem;
            font-family: 'Inter', monospace;
            font-weight: 500;
            text-align: center;
            background-color: #ffffff;
            transition: all 0.2s ease;
        }

        .dca-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
            background-color: #fafbff;
        }

        /* Read-only input styles */
        .dca-input[readonly] {
            background-color: #f8fafc !important;
            cursor: not-allowed !important;
            color: #64748b !important;
        }

        /* DCA Buttons */
        .dca-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .dca-btn-calculate {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .dca-btn-calculate:hover {
            background: #059669;
        }

        .dca-btn-delete {
            background: #ef4444;
            color: white;
        }

        .dca-btn-delete:hover {
            background: #dc2626;
        }

        /* Profit/Loss Colors */
        .profit-positive {
            color: #059669 !important;
            font-weight: 700;
        }

        .profit-negative {
            color: #dc2626 !important;
            font-weight: 700;
        }

        .profit-neutral {
            color: #6b7280 !important;
            font-weight: 600;
        }

        /* ===========================
           DARK MODE ENHANCEMENTS
           =========================== */

        /* Dark mode toggle button */
        #darkModeToggle {
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
        }

        .dark #darkModeToggle {
            box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.1);
            background: linear-gradient(135deg, #475569, #334155);
        }

        #darkModeToggle:hover {
            transform: scale(1.05);
        }

        #darkModeToggle:active {
            transform: scale(0.95);
        }

        /* Toggle switch animation */
        #darkModeToggle .absolute {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        .dark #darkModeToggle .absolute {
            background: linear-gradient(135deg, #1e293b, #0f172a);
            border: 2px solid rgba(147, 197, 253, 0.3);
        }

        /* Dark mode scrollbar */
        .dark ::-webkit-scrollbar-track {
            background: #1e293b;
        }

        .dark ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #475569, #334155);
        }

        .dark ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #64748b, #475569);
        }

        /* Dark mode form inputs */
        .dark input[type="number"],
        .dark input[type="text"],
        .dark select {
            background-color: #1e293b;
            border-color: #475569;
            color: #f1f5f9;
        }

        .dark input[type="number"]:focus,
        .dark input[type="text"]:focus,
        .dark select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        /* Dark mode buttons */
        .dark button:not(.bg-gradient-to-r):not(.dca-btn) {
            background-color: #374151;
            color: #f9fafb;
            border-color: #4b5563;
        }

        .dark button:not(.bg-gradient-to-r):not(.dca-btn):hover {
            background-color: #4b5563;
        }

        /* Dark mode cards */
        .dark .bg-white\/90 {
            background-color: rgba(30, 41, 59, 0.9);
        }

        .dark .bg-white\/95 {
            background-color: rgba(30, 41, 59, 0.95);
        }

        /* Dark mode text colors */
        .dark .text-slate-800 {
            color: #f1f5f9 !important;
        }

        .dark .text-slate-600 {
            color: #cbd5e1 !important;
        }

        .dark .text-slate-700 {
            color: #e2e8f0 !important;
        }

        /* Dark mode borders */
        .dark .border-slate-200 {
            border-color: #475569;
        }

        .dark .border-slate-300 {
            border-color: #64748b;
        }

        /* Dark mode shadows */
        .dark .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
        }

        .dark .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
        }

        /* Dark mode table improvements */
        .dark #overallSummaryTable,
        .dark .dca-table {
            background-color: #1e293b;
        }

        .dark #overallSummaryTable th,
        .dark .dca-table th {
            background: linear-gradient(135deg, #334155 0%, #475569 100%);
            color: #f1f5f9;
            border-bottom-color: #64748b;
        }

        .dark #overallSummaryTable td,
        .dark .dca-table td {
            background-color: #1e293b;
            color: #e2e8f0;
            border-bottom-color: #334155;
        }

        .dark #overallSummaryTable tbody tr:hover,
        .dark .dca-table tbody tr:hover {
            background-color: #334155;
        }

        /* Dark mode footer */
        .dark footer {
            background-color: #0f172a;
            border-top: 1px solid #334155;
        }

        /* Dark mode modal */
        .dark #customModal .bg-white {
            background-color: #1e293b;
        }

        .dark #customModal .bg-slate-50 {
            background-color: #0f172a;
        }

        /* Dark mode status indicators */
        .dark .bg-green-100 {
            background-color: rgba(34, 197, 94, 0.2);
        }

        .dark .bg-red-100 {
            background-color: rgba(239, 68, 68, 0.2);
        }

        .dark .bg-blue-100 {
            background-color: rgba(59, 130, 246, 0.2);
        }

        .dark .bg-slate-50 {
            background-color: #1e293b;
        }

        .dark .bg-slate-100 {
            background-color: #334155;
        }

        /* Dark mode gradient backgrounds */
        .dark .bg-gradient-to-br.from-blue-50 {
            background: linear-gradient(to bottom right, rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.6));
        }

        .dark .bg-gradient-to-br.from-green-50 {
            background: linear-gradient(to bottom right, rgba(20, 83, 45, 0.8), rgba(22, 101, 52, 0.6));
        }

        .dark .bg-gradient-to-br.from-purple-50 {
            background: linear-gradient(to bottom right, rgba(88, 28, 135, 0.8), rgba(107, 33, 168, 0.6));
        }

        .dark .bg-gradient-to-br.from-red-50 {
            background: linear-gradient(to bottom right, rgba(127, 29, 29, 0.8), rgba(153, 27, 27, 0.6));
        }

        /* Dark mode smooth transitions */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }

        /* Dark mode focus states */
        .dark input:focus,
        .dark select:focus,
        .dark button:focus {
            outline: 2px solid rgba(59, 130, 246, 0.5);
            outline-offset: 2px;
        }



        /* Result Display */
        .result-display {
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            min-height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .result-positive {
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            color: #065f46;
            border: 1px solid #10b981;
        }

        .result-negative {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .result-neutral {
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            color: #374151;
            border: 1px solid #9ca3af;
        }



        /* تحسين النصوص للشاشات الصغيرة */
        @media (max-width: 768px) {
            #overallSummaryTable th {
                padding: 0.75rem 0.5rem !important;
                font-size: 0.75rem !important;
            }

            #overallSummaryTable td {
                padding: 1rem 0.5rem !important;
            }

            #overallSummaryTable .font-mono {
                font-size: 0.8rem !important;
            }

            #overallSummaryTable th .w-8 {
                width: 1.5rem !important;
                height: 1.5rem !important;
            }

            #overallSummaryTable td .w-12 {
                width: 2.5rem !important;
                height: 2.5rem !important;
            }
        }

        @media (max-width: 640px) {
            #overallSummaryTable th {
                padding: 0.5rem 0.25rem !important;
                font-size: 0.7rem !important;
            }

            #overallSummaryTable td {
                padding: 0.75rem 0.25rem !important;
            }

            #overallSummaryTable .font-mono {
                font-size: 0.75rem !important;
            }

            #overallSummaryTable th span {
                display: none;
            }

            #overallSummaryTable th .w-8 {
                width: 1.25rem !important;
                height: 1.25rem !important;
            }

            #overallSummaryTable td .w-12 {
                width: 2rem !important;
                height: 2rem !important;
            }
        }

        /* تحسين الأزرار في رسائل الجدول */
        #overallSummaryTable button {
            transition: all 0.2s ease;
        }

        #overallSummaryTable button:hover {
            transform: translateY(-1px);
        }

        /* تحسين الانيميشن للأيقونات */
        .animate-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Custom Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideLeft {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideRight {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
            to { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes rotateSlow {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes bounceGentle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .animate-bounce-gentle {
            animation: bounceGentle 4s ease-in-out infinite;
        }

        /* Enhanced Glass morphism effects */
        .glass-effect {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced card styling */
        .enhanced-card {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .enhanced-card:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        /* Hover effects for cards */
        .card-hover:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* Gradient text animation */
        .gradient-text-animated {
            background: linear-gradient(-45deg, #2563eb, #8b5cf6, #06b6d4, #10b981);
            background-size: 400% 400%;
            animation: gradientShift 4s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Pulse glow effect */
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }

        @keyframes pulseGlow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.4); }
            to { box-shadow: 0 0 40px rgba(59, 130, 246, 0.8); }
        }

        /* Enhanced Text Contrast - Dark Mode */
        .dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
            color: #f8fafc !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .dark p:not(.text-white):not(.text-green-200):not(.text-blue-200):not(.text-purple-200):not(.text-red-200):not(.text-orange-200):not(.text-yellow-200) {
            color: #e2e8f0 !important;
        }

        .dark .text-slate-600 {
            color: #cbd5e1 !important;
        }

        .dark .text-slate-500 {
            color: #94a3b8 !important;
        }

        .dark .text-slate-400 {
            color: #64748b !important;
        }

        /* Gradient Text Improvements - Dark Mode */
        .dark .bg-clip-text.text-transparent {
            background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%) !important;
            -webkit-background-clip: text !important;
            background-clip: text !important;
            color: transparent !important;
        }

        /* Header Text Contrast */
        .dark header h1 {
            background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%) !important;
            -webkit-background-clip: text !important;
            background-clip: text !important;
            color: transparent !important;
        }

        .dark header p {
            color: #e2e8f0 !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Section Titles Dark Mode */
        .dark section h2 {
            color: #f8fafc !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .dark section h3 {
            color: #f1f5f9 !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Label Text Dark Mode */
        .dark label {
            color: #e2e8f0 !important;
        }

        /* Status Text Dark Mode */
        .dark #apiStatus,
        .dark #coinStatus {
            color: #cbd5e1 !important;
        }

        /* Import Progress Styles */
        .import-progress {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 300px;
            text-align: center;
        }

        .import-progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .import-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .import-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
            margin-left: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</body>
</html>