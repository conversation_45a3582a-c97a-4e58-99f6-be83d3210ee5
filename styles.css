/* ===============================
   MODERN CSS VARIABLES - متغيرات CSS الحديثة
   =============================== */
:root {
        /* Crypto-themed gradients */
        --primary-gradient: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        --secondary-gradient: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
        --accent-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
        --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        --dark-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);

        /* Modern color palette */
        --crypto-primary: #1e40af;
        --crypto-secondary: #7c3aed;
        --crypto-accent: #06b6d4;
        --crypto-success: #10b981;
        --crypto-danger: #ef4444;
        --crypto-warning: #f59e0b;
        --crypto-dark: #0f172a;
        --crypto-light: #f8fafc;

        /* Enhanced color system */
        --bg-color: #f8fafc;
        --section-bg: rgba(255, 255, 255, 0.8);
        --text-color: #1e293b;
        --text-muted: #64748b;
        --text-light: #f8fafc;
        --border-color: #e2e8f0;
        --input-bg: #ffffff;
        --input-border: #cbd5e1;
        --hover-bg: #f1f5f9;
        --table-header-bg: #f8fafc;
        --positive-color: #10b981;
        --negative-color: #ef4444;
        --neutral-color: #64748b;

        /* Enhanced shadows and effects */
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

        /* Typography */
        --font-family-arabic: 'Tajawal', 'Segoe UI', sans-serif;
        --font-family-inter: 'Inter', 'Segoe UI', sans-serif;
        --font-family-mono: 'Courier New', 'Monaco', monospace;

        /* Transitions */
        --transition-fast: 0.15s ease-in-out;
        --transition-normal: 0.3s ease-in-out;
        --transition-slow: 0.5s ease-in-out;
    }

/* ===============================
   DARK MODE VARIABLES - متغيرات الوضع الليلي
   =============================== */
.dark,
.dark-mode {
        /* Dark mode gradients */
        --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        --secondary-gradient: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
        --accent-gradient: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
        --success-gradient: linear-gradient(135deg, #059669 0%, #10b981 100%);
        --danger-gradient: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        --warning-gradient: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
        --dark-gradient: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);

        /* Dark mode colors */
        --crypto-primary: #3b82f6;
        --crypto-secondary: #a855f7;
        --crypto-accent: #0891b2;
        --crypto-success: #10b981;
        --crypto-danger: #ef4444;
        --crypto-warning: #f59e0b;
        --crypto-dark: #0f172a;
        --crypto-light: #f8fafc;

        /* Dark mode color system */
        --bg-color: #0f172a;
        --section-bg: rgba(30, 41, 59, 0.8);
        --text-color: #e2e8f0;
        --text-muted: #94a3b8;
        --text-light: #f8fafc;
        --border-color: #334155;
        --input-bg: #1e293b;
        --input-border: #475569;
        --hover-bg: #334155;
        --table-header-bg: #1e293b;
        --positive-color: #34d399;
        --negative-color: #f87171;
        --neutral-color: #94a3b8;

        /* Dark mode shadows */
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }

/* ===============================
   ENHANCED STYLES - تحسينات إضافية
   =============================== */

/* PnL Color Classes */
.pnl-positive {
    color: var(--positive-color) !important;
    font-weight: 600;
}

.pnl-negative {
    color: var(--negative-color) !important;
    font-weight: 600;
}

.pnl-neutral {
    color: var(--neutral-color) !important;
    font-weight: 500;
}

/* Enhanced Table Styles */
.summary-table tbody tr:hover {
    background-color: var(--hover-bg);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
}

/* Enhanced Button Hover Effects */
button:hover {
    transform: translateY(-1px);
    transition: all var(--transition-fast);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--crypto-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Focus States */
input:focus,
select:focus,
button:focus {
    outline: none;
    ring: 2px;
    ring-color: var(--crypto-primary);
    ring-opacity: 0.5;
    border-color: var(--crypto-primary);
    transition: all var(--transition-fast);
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Enhanced Backdrop Blur */
.backdrop-blur-xl {
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
}

/* ===============================
   COMPATIBILITY FIXES - إصلاحات التوافق
   =============================== */

/* Ensure proper theme inheritance */
.dark *,
.dark-mode * {
    border-color: var(--border-color);
}

/* Fix for old CSS classes */
.dark .page-header,
.dark-mode .page-header {
    background: var(--crypto-dark);
    color: var(--text-light);
}

.dark .container,
.dark-mode .container {
    color: var(--text-color);
}

/* Fix for summary table in dark mode */
.dark .summary-table,
.dark-mode .summary-table,
.dark .repurchase-table,
.dark-mode .repurchase-table {
    background: var(--section-bg);
    color: var(--text-color);
}

.dark .summary-table th,
.dark-mode .summary-table th,
.dark .repurchase-table th,
.dark-mode .repurchase-table th {
    background: var(--table-header-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

.dark .summary-table td,
.dark-mode .summary-table td,
.dark .repurchase-table td,
.dark-mode .repurchase-table td {
    border-color: var(--border-color);
    color: var(--text-color);
}

/* Fix for form elements */
.dark input,
.dark-mode input,
.dark select,
.dark-mode select,
.dark textarea,
.dark-mode textarea {
    background: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

.dark .output-field,
.dark-mode .output-field {
    background: var(--input-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

/* Fix for sections */
.dark .section,
.dark-mode .section,
.dark .overall-summary-section,
.dark-mode .overall-summary-section {
    background: var(--section-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

/* Fix for controls bar */
.dark .controls-bar,
.dark-mode .controls-bar {
    background: var(--section-bg);
    border-color: var(--border-color);
}

/* Fix for buttons */
.dark .control-btn,
.dark-mode .control-btn {
    color: white;
}

/* Fix for theme toggle button positioning */
#themeToggleBtn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark #themeToggleBtn,
.dark-mode #themeToggleBtn {
    background: var(--section-bg);
    border: 1px solid var(--border-color);
}

#themeToggleBtn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        -webkit-tap-highlight-color: transparent;
    }

    html {
        height: 100%;
        scroll-behavior: smooth;
    }

    body {
        font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: var(--bg-color);
        color: var(--text-color);
        /* خلفية عادية بدون أي تدرج أو صورة */
        background-image: none;
        line-height: 1.6;
        margin: 0;
        font-size: 16px;
        transition: background-color 0.3s ease, color 0.3s ease;
        display: flex;
        flex-direction: column;
        min-height: 100%;
    }

    .page-header {
        text-align: center;
        padding: 12px 15px;
        background: var(--dark-header-bg);
        color: var(--text-light);
        font-size: 0.9em;
        border-top: 3px solid var(--primary-color);
        box-shadow: 0 -2px 10px var(--shadow-color);
        line-height: 1.6;
        margin-top: 30px;
        flex-shrink: 0;
        order: 1;
    }

    .page-header strong {
        font-weight: var(--font-weight-semibold);
    }

    .page-header a {
        color: var(--header-link-color);
        text-decoration: none;
        font-weight: var(--font-weight-normal);
        margin: 0 5px;
        transition: color 0.2s ease;
    }

    .page-header a:hover {
        text-decoration: underline;
        color: var(--header-link-hover-color);
    }

    .page-header a.telegram-link {
        display: inline-block;
        background: var(--primary-gradient);
        color: white !important;
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: var(--font-weight-semibold);
        margin: 5px 0;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        box-shadow: var(--button-shadow);
    }

    .page-header a.telegram-link:hover {
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: var(--button-shadow-hover);
    }

    .page-header small {
        font-size: 0.85em;
        color: var(--header-link-color);
    }

    .container {
        max-width: 1920px;
        margin: 20px auto;
        padding: 0 25px;
        flex-grow: 1;
    }

    h1 {
        text-align: center;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        margin: 25px 0 20px;
        font-weight: var(--font-weight-bold);
        font-size: 2.2em;
        transition: all 0.3s ease;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        padding-bottom: 15px;
        position: relative;
    }

    h1::after {
        content: "";
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: var(--primary-gradient);
        border-radius: 2px;
        transition: all 0.3s ease;
    }

    h2 {
        margin-top: 0;
        background: var(--secondary-gradient);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-size: 1.5em;
        border-bottom: 2px solid var(--border-color);
        font-weight: var(--font-weight-semibold);
        transition: all 0.3s ease;
    }

    h3 {
        font-size: 1.3em;
        color: var(--primary-color);
        margin-bottom: 15px;
        margin-top: 0;
        font-weight: var(--font-weight-semibold);
        transition: color 0.3s ease;
    }

    .overall-summary-section {
        background-color: var(--section-bg);
        padding: 25px;
        margin-bottom: 25px;
        border-radius: 12px;
        box-shadow: 0 4px 15px var(--shadow-color);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .overall-summary-section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .summary-table-container {
        max-height: 280px;
        overflow-y: auto;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border: 1px solid var(--border-color);
        border-radius: 10px;
        margin-bottom: 20px;
        background-color: var(--section-bg);
        transition: all 0.3s ease;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
    }

    .table-responsive {
        max-height: auto;
        overflow-y: auto;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border: 1px solid var(--border-color);
        border-radius: 10px;
        margin-bottom: 20px;
        background-color: var(--section-bg);
        transition: all 0.3s ease;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
    }

    .summary-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 0.95em;
    }

    .summary-table th,
    .summary-table td {
        border-bottom: 1px solid var(--border-color);
        padding: 14px 16px;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        transition: all 0.3s ease;
    }

    .summary-table th {
        background-color: var(--table-header-bg);
        color: var(--text-color);
        position: sticky;
        top: 0;
        z-index: 1;
        font-weight: var(--font-weight-bold);
        border-top: none;
        border-bottom-width: 2px;
    }

    .summary-table th i {
        margin-left: 8px;
    }

    .summary-table td:first-child,
    .summary-table th:first-child {
        border-right: 1px solid var(--border-color);
    }

    .summary-table td:last-child,
    .summary-table th:last-child {
        border-left: 1px solid var(--border-color);
    }

    .summary-table tr:last-child td {
        border-bottom: none;
    }

    .summary-table tbody tr:nth-child(even) td {
        background-color: var(--table-header-bg);
    }

    .summary-table tr:hover td {
        background-color: var(--hover-bg);
    }

    .summary-table .coin-symbol {
        text-align: right;
        padding-right: 15px;
        font-weight: var(--font-weight-semibold);
    }

    .summary-table .number-col {
        font-family: 'Courier New', Courier, monospace;
        direction: ltr;
        text-align: center;
        font-weight: var(--font-weight-semibold);
    }

    .summary-table .pnl-positive,
    .totals-positive {
        color: var(--positive-color) !important;
        font-weight: var(--font-weight-bold);
    }

    .summary-table .pnl-negative,
    .totals-negative {
        color: var(--negative-color) !important;
        font-weight: var(--font-weight-bold);
    }

    .summary-table .error {
        color: var(--negative-color);
        font-style: italic;
        font-size: 0.9em;
        font-weight: var(--font-weight-normal);
    }



    .summary-totals {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        background-color: var(--section-bg);
        padding: 20px;
        border-radius: 10px;
        border: 1px solid var(--border-color);
        font-weight: var(--font-weight-bold);
        text-align: center;
        transition: all 0.3s ease;
    }

    .summary-totals div {
        display: flex;
        flex-direction: column;
    }

    .summary-totals label {
        font-size: 0.95em;
        color: var(--text-muted);
        margin-bottom: 8px;
        font-weight: var(--font-weight-normal);
    }

    .summary-totals span {
        font-size: 1.4em;
        font-family: 'Courier New', Courier, monospace;
        font-weight: var(--font-weight-bold);
    }

    /* شريط التحكم المحسن */
    .controls-bar {
        display: grid;
        grid-template-columns: 1.3fr 1.3fr repeat(6, 1fr);
        gap: 16px;
        margin: 25px 0 20px;
        padding: 20px;
        background: var(--section-bg);
        border-radius: 12px;
        box-shadow: 0 4px 15px var(--shadow-color);
        border: 1px solid var(--border-color);
        align-items: center;
        transition: all 0.3s ease;
    }

    .controls-bar .control-group {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        min-width: 0;
    }

    .controls-bar label {
        font-size: 1em;
        font-weight: var(--font-weight-semibold);
        margin-left: 8px;
        color: var(--primary-color);
        white-space: nowrap;
    }

    .controls-bar input[type="text"],
    .controls-bar select {
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid var(--input-border);
        background: var(--input-bg);
        font-size: 1em;
        width: 100%;
        color: var(--text-color);
        transition: all 0.3s ease;
        font-family: 'Tajawal', sans-serif;
    }

    .controls-bar input[type="text"]:focus,
    .controls-bar select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px var(--focus-ring-color);
    }

    .controls-bar button,
    .controls-bar button.control-btn {
        padding: 12px 0;
        font-size: 1em;
        font-weight: var(--font-weight-semibold);
        border-radius: 8px;
        border: none;
        background: var(--secondary-gradient);
        color: white;
        transition: all 0.3s ease;
        box-shadow: var(--button-shadow);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        height: 48px;
        width: 100%;
    }

    .controls-bar button.control-btn.danger {
        background: var(--danger-gradient);
    }

    .controls-bar button.control-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: var(--text-muted);
    }

    .controls-bar button.control-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: var(--button-shadow-hover);
    }

    .status-indicators {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        margin: 0 8px;
        min-width: 0;
        width: 100%;
        font-size: 0.95em;
        color: var(--text-muted);
    }

    #tradingViewChartBtn {
        padding: 12px 40px;
        font-size: 1.05em;
        min-height: 44px;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 10px;
        font-weight: var(--font-weight-semibold);
        cursor: pointer;
        background: var(--primary-gradient);
        color: white;
        border: none;
        border-radius: 8px;
        box-shadow: var(--button-shadow);
        transition: all 0.3s ease;
    }

    #tradingViewChartBtn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: var(--button-shadow-hover);
    }

    #tradingViewChartBtn:disabled {
        background: var(--text-muted);
        color: var(--text-light);
        cursor: not-allowed;
        opacity: 0.7;
    }

    .details-content-wrapper {
        display: grid;
        grid-template-columns: 0.9fr 1.6fr 1fr;
        gap: 30px;
        align-items: flex-start;
    }

    .section {
        background-color: var(--section-bg);
        padding: 25px;
        border-radius: 12px;
        border: 1px solid var(--border-color);
        box-shadow: 0 4px 15px var(--shadow-color);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .section > *:last-child {
        margin-bottom: 0;
    }

    .section-title {
        margin-bottom: 20px;
        font-size: 1.3em;
        font-weight: var(--font-weight-bold);
        color: var(--primary-color);
        display: flex;
        align-items: center;
        gap: 10px;
    }

    /* إضافة مسافة بين العناوين والمحتوى */
    .section-title, h2, h3, h4 {
        margin-bottom: 20px;
    }

    /* تحسين مظهر العناوين المحددة في الصورة */
    .الوضع-الحالي-والأهداف, .التقرير, .إدخال-تفاصيل-الصفقة {
        margin-bottom: 25px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-color);
    }

    /* إضافة مسافة بعد الجداول */
    .table-responsive, .summary-table-container {
        margin-bottom: 25px;
    }

    /* تحسين المسافة بين عناصر النموذج */
    .form-group {
        margin-bottom: 20px;
    }

    /* تحسين المسافة في الأقسام */
    .section {
        padding: 25px;
    }

    .section > *:first-child {
        margin-top: 0;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px 15px;
        margin-bottom: 25px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    label {
        margin-bottom: 8px;
        font-weight: var(--font-weight-semibold);
        color: var(--text-muted);
        font-size: 0.95em;
    }

    input[type="text"],
    input[type="number"],
    select {
        padding: 12px 16px;
        border: 1px solid var(--input-border);
        border-radius: 8px;
        font-size: 1em;
        width: 100%;
        background-color: var(--input-bg);
        color: var(--text-color);
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        font-family: 'Tajawal', sans-serif;
    }

    input[type="number"] {
        -moz-appearance: textfield;
    }

    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    input[type="text"]:focus,
    input[type="number"]:focus,
    select:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px var(--focus-ring-color);
    }

    .output-field,
    .input-field-read-only {
        padding: 12px 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        background-color: var(--output-field-bg);
        font-weight: var(--font-weight-bold);
        min-height: 46px;
        display: flex;
        align-items: center;
        color: var(--output-field-text);
        font-size: 1em;
        word-break: break-all;
        width: 100%;
        font-family: 'Courier New', Courier, monospace;
        transition: all 0.3s ease;
        direction: ltr;
        text-align: left;
    }

    .output-field.error {
        color: var(--negative-color);
        background-color: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.3);
    }

    .market-price-display-group {
        grid-column: span 2;
        display: flex;
        align-items: flex-end;
        gap: 15px;
        margin-top: 10px;
    }

    .market-price-display-group .form-group {
        flex-grow: 1;
        margin-bottom: 0;
    }

    #marketPriceDisplay {
        font-size: 1.2em;
        font-weight: var(--font-weight-bold);
        color: var(--primary-color);
        background-color: var(--bg-color);
    }

    .auto-refresh-group {
        grid-column: span 2;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 0.95em;
        margin-top: 10px;
        background-color: var(--output-field-bg);
        padding: 10px 15px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }

    .auto-refresh-group input[type="checkbox"] {
        margin-left: 5px;
        width: 18px;
        height: 18px;
        accent-color: var(--primary-color);
    }

    .auto-refresh-group label {
        margin-bottom: 0;
        font-weight: var(--font-weight-normal);
        color: var(--text-muted);
    }

    #targetsSection {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px dashed var(--border-color);
    }

    .targets-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .target-pair {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        align-items: end;
    }

    .targets-grid label {
        font-size: 0.9em;
        text-align: center;
        margin-bottom: 5px;
        display: block;
        font-weight: var(--font-weight-normal);
    }

    .targets-grid .form-group {
        margin-bottom: 0;
    }

    .targets-grid input[type="number"] {
        text-align: center;
        padding: 10px 12px;
    }

    .targets-grid .output-field {
        font-size: 1.1em;
        text-align: center;
        padding: 11px 12px;
        min-height: 42px;
        font-weight: var(--font-weight-bold);
    }

    .sl-group {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid var(--border-color);
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .sl-input input {
        border-color: var(--negative-color);
        background-color: rgba(239, 68, 68, 0.1);
    }

    .sl-input input:focus {
        border-color: var(--negative-color);
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
    }

    .sl-output {
        background-color: var(--sl-output-bg) !important;
        color: var(--sl-output-text) !important;
        border-color: var(--sl-output-border) !important;
        font-weight: var(--font-weight-bold);
    }

    .section-repurchase {
        overflow: auto;
    }

    .repurchase-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-top: 15px;
        font-size: 0.95em;
    }

    .repurchase-table th,
    .repurchase-table td {
        border-bottom: 1px solid var(--border-color);
        padding: 10px 8px;
        text-align: center;
        white-space: nowrap;
    }

    .repurchase-table th {
        background-color: var(--table-header-bg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color);
        position: sticky;
        top: 0;
        z-index: 1;
        border-top: 1px solid var(--border-color);
    }

    .repurchase-table td:first-child,
    .repurchase-table th:first-child {
        border-right: 1px solid var(--border-color);
    }

    .repurchase-table td:last-child,
    .repurchase-table th:last-child {
        border-left: 1px solid var(--border-color);
    }

    .repurchase-table tr:last-child td {
        border-bottom: none;
    }

    .repurchase-table tr:hover td {
        background-color: var(--hover-bg);
    }

    .repurchase-table input[type="number"] {
        width: 90%;
        padding: 8px 10px;
        font-size: 1em;
        text-align: center;
        margin: 0 auto;
        display: block;
    }

    .repurchase-table .output-field {
        font-size: 1em;
        padding: 8px;
        min-height: 38px;
        width: auto;
        background-color: transparent;
        border: none;
        font-family: 'Courier New', Courier, monospace;
        font-weight: var(--font-weight-bold);
        color: var(--text-color);
        text-align: center;
    }

    .repurchase-table .repurchase-pnl,
    .repurchase-table .repurchase-pnl-percent {
        font-family: 'Courier New', Courier, monospace;
        font-weight: var(--font-weight-bold);
        direction: ltr;
        text-align: center;
    }

    .down-percent {
        font-weight: var(--font-weight-semibold);
        min-width: 60px;
        display: inline-block;
        font-family: 'Courier New', Courier, monospace;
        direction: ltr;
        padding: 4px 6px;
        border-radius: 4px;
        font-size: 0.9em;
    }

    .down-percent.positive {
        color: var(--positive-color);
        background-color: rgba(16, 185, 129, 0.1);
    }

    .down-percent.negative {
        color: var(--negative-color);
        background-color: rgba(239, 68, 68, 0.1);
    }

    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 20px;
    }

    .summary-item {
        background-color: var(--bg-color);
        padding: 18px;
        border-radius: 10px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
    }

    .summary-item label {
        font-size: 0.9em;
        color: var(--text-muted);
        display: block;
        margin-bottom: 8px;
        font-weight: var(--font-weight-normal);
    }

    .summary-item .value {
        font-size: 1.3em;
        font-weight: var(--font-weight-bold);
        color: var(--primary-color);
        word-wrap: break-word;
        margin-top: auto;
        font-family: 'Courier New', Courier, monospace;
        direction: ltr;
        text-align: left;
    }

    .pnl-positive {
        color: var(--positive-color) !important;
    }

    .pnl-negative {
        color: var(--negative-color) !important;
    }

    .pnl-neutral {
        color: var(--text-color) !important;
    }

    .summary-empty-row {
        text-align: center;
        padding: 30px;
        font-weight: normal;
        color: var(--text-muted);
    }

    #themeToggleBtn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--dark-gradient);
        color: white;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8em;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 1050;
        cursor: pointer;
        opacity: 0.8;
        transition: all 0.3s ease;
    }

    #themeToggleBtn:hover {
        transform: translateY(-3px) scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        opacity: 1;
    }

    /* تأثيرات إضافية */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .section {
        animation: fadeIn 0.5s ease forwards;
    }

    .section:nth-child(1) { animation-delay: 0.1s; }
    .section:nth-child(2) { animation-delay: 0.2s; }
    .section:nth-child(3) { animation-delay: 0.3s; }

    /* استجابة للشاشات المختلفة */
    @media (max-width: 1600px) {
        .controls-bar {
            grid-template-columns: 1.2fr 1.2fr repeat(6, 1fr);
        }
    }

    @media (max-width: 1400px) {
        .controls-bar {
            grid-template-columns: 1.3fr 1.3fr repeat(4, 1fr);
        }
        .summary-table-container {
            overflow-x: auto;
        }
    }

    @media (max-width: 1200px) {
        .details-content-wrapper {
            grid-template-columns: 1fr 1fr;
        }
        .details-content-wrapper > .section-current-targets:nth-child(3) {
            grid-column: 1 / -1;
        }
        .controls-bar {
            grid-template-columns: repeat(4, 1fr);
        }
        .summary-table-container {
            max-height: 350px;
        }
    }

    @media (max-width: 992px) {
        .summary-table {
            font-size: 0.9em;
        }
        .summary-table th,
        .summary-table td {
            padding: 10px 12px;
        }
        .summary-totals {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        }
        .details-content-wrapper {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        .repurchase-table {
            min-width: 700px;
        }
    }

    @media (max-width: 900px) {
        .controls-bar {
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
        .overall-summary-section-header {
            flex-direction: column;
            gap: 15px;
            align-items: flex-start;
        }
    }

    @media (max-width: 768px) {
        .container {
            padding: 0 20px;
            margin: 15px auto;
        }
        h1 {
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
        }
        h2 {
            font-size: 1.4em;
        }
        .summary-totals {
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            padding: 15px;
        }
        .controls-bar {
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 20px 0 15px;
        }
        .section {
            padding: 20px;
        }
        .form-grid {
            grid-template-columns: 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .market-price-display-group,
        .auto-refresh-group {
            grid-column: auto;
        }
        .targets-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        .target-pair {
            grid-template-columns: 1fr 1fr;
        }
        .sl-group {
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 15px;
            padding-top: 15px;
        }
        .summary-item {
            padding: 15px;
        }
        .summary-item .value {
            font-size: 1.2em;
        }
        #tradingview_cryptochart {
            height: 350px !important;
        }
    }

    @media (max-width: 700px) {
        .controls-bar .control-group {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
        }
        .controls-bar label {
            margin-left: 0;
            white-space: normal;
        }
        .controls-bar button,
        .controls-bar button.control-btn {
            height: 44px;
            font-size: 0.95em;
        }
        .status-indicators {
            grid-column: 1 / -1;
        }
    }

    @media (max-width: 576px) {
        .controls-bar {
            grid-template-columns: 1fr;
            gap: 10px;
            padding: 15px;
        }
        .summary-totals {
            grid-template-columns: 1fr;
        }
        .summary-totals span {
            font-size: 1.3em;
        }
        .page-header {
            padding: 10px;
            font-size: 0.85em;
        }
        .page-header a.telegram-link {
            padding: 6px 10px;
            margin: 3px 0;
        }
    }

    @media (max-width: 500px) {
        body {
            font-size: 15px;
        }
        .container {
            padding: 0 12px;
        }
        h1 {
            font-size: 1.5em;
        }
        h1::after {
            width: 80px;
            height: 3px;
        }
        h2 {
            font-size: 1.2em;
        }
        .summary-table {
            font-size: 0.8em;
        }
        .summary-table th,
        .summary-table td {
            padding: 8px 6px;
            white-space: normal;
        }
        .section {
            padding: 15px;
            border-radius: 10px;
        }
        .repurchase-table {
            font-size: 0.85em;
        }
        .repurchase-table th,
        .repurchase-table td {
            padding: 8px 4px;
            white-space: normal;
        }
        .summary-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
        .target-pair {
            grid-template-columns: 1fr;
            gap: 10px;
        }
        #themeToggleBtn {
            width: 45px;
            height: 45px;
            font-size: 1.3em;
            bottom: 15px;
            right: 15px;
        }
        #tradingview_cryptochart {
            height: 300px !important;
        }
    }

    /* تحسينات إضافية للشاشات الصغيرة جدًا */
    @media (max-width: 375px) {
        body {
            font-size: 14px;
        }
        .container {
            padding: 0 10px;
            margin: 10px auto;
        }
        h1 {
            font-size: 1.4em;
            margin: 15px 0;
        }
        .section {
            padding: 12px;
        }
        .summary-table th,
        .summary-table td {
            padding: 6px 4px;
            font-size: 0.9em;
        }
        .summary-item {
            padding: 12px;
        }
        .summary-item .value {
            font-size: 1.1em;
        }
        input[type="text"],
        input[type="number"],
        select,
        .output-field {
            padding: 10px 12px;
            font-size: 0.95em;
        }
        #tradingview_cryptochart {
            height: 250px !important;
        }
    }

    /* تحسينات إضافية للشاشات الصغيرة جدًا */
    @media (max-width: 400px) {
        .container {
            padding: 0 4px;
        }
        h1 {
            font-size: 1.1em;
            padding-bottom: 8px;
        }
        .section {
            padding: 7px;
            border-radius: 7px;
        }
        .summary-table th,
        .summary-table td {
            padding: 3px 2px;
            font-size: 0.8em;
        }
        .summary-item {
            padding: 7px;
        }
        .summary-item .value {
            font-size: 0.95em;
        }
        .controls-bar button,
        .controls-bar button.control-btn {
            font-size: 0.85em;
            padding: 8px 0;
            height: 36px;
        }
        #themeToggleBtn {
            width: 35px;
            height: 35px;
            font-size: 1em;
            bottom: 8px;
            right: 8px;
        }
        .repurchase-table {
            min-width: 400px;
            font-size: 0.75em;
        }
        .repurchase-table th,
        .repurchase-table td {
            padding: 4px 2px;
        }
        .summary-totals label,
        .summary-totals span {
            font-size: 0.85em;
        }
        .form-group label {
            font-size: 0.8em;
        }
        input[type="text"],
        input[type="number"],
        select,
        .output-field {
            padding: 7px 6px;
            font-size: 0.85em;
        }
        .market-price-display-group,
        .auto-refresh-group {
            flex-direction: column;
            gap: 4px;
        }
    }
    /* تحسين عرض الجداول في الشاشات الضيقة جدًا */

/* Notification Styles */
.notification-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--section-bg);
  color: var(--text-color);
  border-radius: 8px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 9999;
  min-width: 300px;
  max-width: 80vw;
  animation: slideDown 0.3s forwards;
  border-right: 4px solid var(--primary-color);
}

.notification-toast.success {
  border-right-color: #10b981;
}

.notification-toast.error {
  border-right-color: #ef4444;
}

.notification-toast.warning {
  border-right-color: #f59e0b;
}

.notification-icon {
  font-size: 1.2em;
}

.notification-message {
  flex: 1;
  font-size: 0.95em;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.2em;
  cursor: pointer;
  padding: 0 4px;
}

.notification-toast.hide {
  animation: slideUp 0.3s forwards;
}

@keyframes slideDown {
  from { opacity: 0; transform: translate(-50%, -20px); }
  to { opacity: 1; transform: translate(-50%, 0); }
}

@keyframes slideUp {
  from { opacity: 1; transform: translate(-50%, 0); }
  to { opacity: 0; transform: translate(-50%, -20px); }
}

/* Modal Styles */
.custom-modal-overlay {
  position: fixed;
  top: 0; right: 0; bottom: 0; left: 0;
  background: rgba(30,41,59,0.45);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s;
}
.custom-modal {
  background: var(--section-bg);
  color: var(--text-color);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  min-width: 320px;
  max-width: 95vw;
  padding: 0;
  overflow: hidden;
  animation: fadeInUp 0.3s;
}
.custom-modal-header {
  padding: 16px 20px 8px 20px;
  font-weight: var(--font-weight-bold);
  font-size: 1.1em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--primary-gradient);
  color: #fff;
}
.custom-modal-close {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.5em;
  cursor: pointer;
  margin-left: 10px;
}
.custom-modal-body {
  padding: 18px 20px;
  font-size: 1em;
  color: var(--text-color);
  background: var(--section-bg);
}
.custom-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 12px 20px 16px 20px;
  background: var(--section-bg);
}
.custom-modal-footer .control-btn {
  padding: 8px 24px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(90deg, #2196f3 0%, #00bcd4 100%);
  color: #fff;
  font-size: 1.08em;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
  box-shadow: 0 2px 8px rgba(33,150,243,0.10);
  margin-right: 6px;
}
.custom-modal-footer .control-btn:hover {
  background: linear-gradient(90deg, #1976d2 0%, #0097a7 100%);
  transform: translateY(-2px) scale(1.04);
}
.custom-modal-footer .control-btn.danger {
  background: linear-gradient(90deg, #e53935 0%, #ff7043 100%);
  color: #fff;
}
.custom-modal-footer .control-btn.danger:hover {
  background: linear-gradient(90deg, #b71c1c 0%, #ff5722 100%);
}
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(40px);}
  to { opacity: 1; transform: translateY(0);}
}

    /* Responsive Design تحسين التجاوب مع جميع الأجهزة */
    @media (max-width: 1200px) {
        .container {
            max-width: 1000px;
            padding: 0 10px;
        }
        .details-content-wrapper {
            grid-template-columns: 1fr 1fr;
            gap: 18px;
        }
    }

    @media (max-width: 900px) {
        .container {
            max-width: 100%;
            padding: 0 5px;
        }
        .details-content-wrapper {
            grid-template-columns: 1fr;
            gap: 12px;
        }
        .controls-bar {
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            padding: 12px;
        }
        .section {
            padding: 12px;
        }
    }

    @media (max-width: 600px) {
        h1 {
            font-size: 1.3em;
            padding-bottom: 8px;
        }
        h2 {
            font-size: 1.1em;
        }
        .controls-bar {
            grid-template-columns: 1fr;
            gap: 8px;
            padding: 8px;
        }
        .summary-totals {
            grid-template-columns: 1fr;
            gap: 8px;
            padding: 8px;
        }
        .section {
            padding: 7px;
            border-radius: 7px;
        }
        .summary-table th,
        .summary-table td,
        .repurchase-table th,
        .repurchase-table td {
            padding: 4px 2px;
            font-size: 0.85em;
        }
    }

    @media (max-width: 400px) {
        h1, h2, h3 {
            font-size: 1em;
        }
        .container {
            padding: 0 2px;
        }
        .section {
            padding: 3px;
        }
        .summary-table th,
        .summary-table td,
        .repurchase-table th,
        .repurchase-table td {
            font-size: 0.7em;
            padding: 2px 1px;
        }
    }

    .summary-table .pair-symbol-col,
    .summary-table th.pair-symbol-col {

       background-color: var(--table-header-bg);
        font-size: 1.1em;
        font-weight: var(--font-weight-bold);
        color: var(--secondary-color);
        min-width: 60px;
        max-width: 90px;
        letter-spacing: 1px;
        border-left: 1px solid var(--border-color);
        border-right: none;
    }

    .coin-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2em; /* مسافة بين الأيقونات */
}
.coin-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #fff;
  border: 1.5px solid #e2e8f0;
  object-fit: contain;
  margin: 0;         /* أزل أي margin سلبي */
  box-shadow: 0 1px 4px rgba(0,0,0,0.07);
}
