const repurchaseTableBody = document.getElementById("repurchaseRows");
      const marketPriceDisplay = document.getElementById("marketPriceDisplay");
      const apiStatusDiv = document.getElementById("apiStatus");
      const autoRefreshCheckbox = document.getElementById(
        "autoRefreshCheckbox",
      );
      const coinSelector = document.getElementById("coinSelector");
      const newCoinNameInput = document.getElementById("newCoinName");
      const coinStatusDiv = document.getElementById("coinStatus");
      const summaryTableBody = document.getElementById("summaryTableBody");
      const totalInvestedSummaryEl = document.getElementById(
        "totalInvestedSummary",
      );
      const totalPnlAmountSummaryEl = document.getElementById(
        "totalPnlAmountSummary",
      );
      const totalCurrentValueSummaryEl = document.getElementById(
        "totalCurrentValueSummary",
      );
      const totalPnlPercentSummaryEl = document.getElementById(
        "totalPnlPercentSummary",
      );

      const currentCoinDisplayElements = [
        document.getElementById("currentCoinDisplay1"),
        document.getElementById("currentCoinDisplay2"),
        document.getElementById("currentCoinDisplay3"),
      ];

      const maxRepurchaseEntries = 10;
      let fetchTimeout,
        autoRefreshIntervalId = null;
      const AUTO_REFRESH_INTERVAL = 30000;
      const LS_KEY_DATA = "cryptoTrackerUniversal_v9_data";
      const AUTO_REFRESH_CHECKBOX_STATE_KEY = 'autoRefreshCheckboxState_v9';
      let allCoinData = {};
      let currentMarketPrices = {};
      let activeCoinSymbol = null;

      function getDefaultCoinDataStructure() {
        const repurchases = Array.from(
          { length: maxRepurchaseEntries },
          () => ({ price: "", amount: "" }),
        );
        return {
          initialEntryPrice: "",
          initialAmountDollars: "",
          repurchases: repurchases,
          targets: { tp1: "", tp2: "", tp3: "", sl: "" },
        };
      }

      function saveAllDataToLocalStorage() {
        if (activeCoinSymbol && allCoinData[activeCoinSymbol]) {
          updateActiveCoinDataInMemory();
        }
        const dataToSave = { coins: allCoinData, active: activeCoinSymbol };
        try {
          localStorage.setItem(LS_KEY_DATA, JSON.stringify(dataToSave));
        } catch (error) {
          console.error("Error saving coin data:", error);
          apiStatusDiv.textContent = "خطأ في حفظ البيانات!";
          apiStatusDiv.style.color = "var(--negative-color)";
        }
      }

      function loadAllDataFromLocalStorage() {
        const savedData = localStorage.getItem(LS_KEY_DATA);
        if (savedData) {
          try {
            const parsedData = JSON.parse(savedData);
            if (
              parsedData &&
              typeof parsedData.coins === "object" &&
              parsedData.coins !== null
            ) {
              allCoinData = parsedData.coins;
              activeCoinSymbol = parsedData.active || null;
              Object.keys(allCoinData).forEach((symbol) => {
                if (!allCoinData[symbol]) {
                  allCoinData[symbol] = getDefaultCoinDataStructure();
                }
                if (
                  !allCoinData[symbol].repurchases ||
                  allCoinData[symbol].repurchases.length !==
                    maxRepurchaseEntries
                ) {
                  const existingRepurchases =
                    allCoinData[symbol].repurchases || [];
                  allCoinData[symbol].repurchases = Array.from(
                    { length: maxRepurchaseEntries },
                    (_, i) =>
                      existingRepurchases[i] || { price: "", amount: "" },
                  );
                }
                if (!allCoinData[symbol].targets) {
                  allCoinData[symbol].targets = {
                    tp1: "",
                    tp2: "",
                    tp3: "",
                    sl: "",
                  };
                }
                currentMarketPrices[symbol] = null;
              });
            } else {
              allCoinData = {};
              activeCoinSymbol = null;
              currentMarketPrices = {};
            }
            return true;
          } catch (error) {
            console.error("Error loading or parsing coin data:", error);
            allCoinData = {};
            activeCoinSymbol = null;
            currentMarketPrices = {};
            localStorage.removeItem(LS_KEY_DATA);
            return false;
          }
        }
        allCoinData = {};
        activeCoinSymbol = null;
        currentMarketPrices = {};
        return false;
      }



      function updateCoinSelector() {
        const previouslySelected = coinSelector.value;
        coinSelector.innerHTML = '<option value="">-- اختر عملة --</option>';
        const coinSymbols = Object.keys(allCoinData).sort();
        coinSymbols.forEach((symbol) => {
          const option = document.createElement("option");
          option.value = symbol;
          option.textContent = symbol;
          coinSelector.appendChild(option);
        });
        if (activeCoinSymbol && allCoinData[activeCoinSymbol]) {
          coinSelector.value = activeCoinSymbol;
        } else if (previouslySelected && allCoinData[previouslySelected]) {
          coinSelector.value = previouslySelected;
          activeCoinSymbol = previouslySelected;
        } else if (coinSymbols.length > 0) {
          coinSelector.value = coinSymbols[0];
          activeCoinSymbol = coinSymbols[0];
        } else {
          activeCoinSymbol = null;
          clearUIFields();
        }
        if (coinSelector.value) {
          displayCoinData(coinSelector.value);
        } else {
          updateCurrentCoinDisplay("لا عملة محددة");
        }
        updateCoinStatus();
      }

      function displayCoinData(symbol) {
        activeCoinSymbol = symbol;
        const data = allCoinData[symbol];
        if (!data) {
          console.warn(`No data for symbol: ${symbol}. Clearing UI.`);
          clearUIFields();
          updateCurrentCoinDisplay(symbol || "خطأ");
          calculateActiveCoinDetails();
          return;
        }
        updateCurrentCoinDisplay(symbol);
        document.getElementById("initialEntryPrice").value =
          data.initialEntryPrice || "";
        document.getElementById("initialAmountDollars").value =
          data.initialAmountDollars || "";
        if (
          data.repurchases &&
          data.repurchases.length === maxRepurchaseEntries
        ) {
          for (let i = 0; i < maxRepurchaseEntries; i++) {
            const priceInput = document.getElementById(
              `repurchasePrice${i + 1}`,
            );
            const amountInput = document.getElementById(
              `repurchaseAmount${i + 1}`,
            );
            if (priceInput) priceInput.value = data.repurchases[i]?.price || "";
            if (amountInput)
              amountInput.value = data.repurchases[i]?.amount || "";
          }
        } else {
          for (let i = 1; i <= maxRepurchaseEntries; i++) {
            const pIn = document.getElementById(`repurchasePrice${i}`);
            if (pIn) pIn.value = "";
            const aIn = document.getElementById(`repurchaseAmount${i}`);
            if (aIn) aIn.value = "";
          }
        }
        if (data.targets) {
          document.getElementById("tpPercent1").value = data.targets.tp1 || "";
          document.getElementById("tpPercent2").value = data.targets.tp2 || "";
          document.getElementById("tpPercent3").value = data.targets.tp3 || "";
          document.getElementById("slPercent").value = data.targets.sl || "";
        } else {
          document.getElementById("tpPercent1").value = "";
          document.getElementById("tpPercent2").value = "";
          document.getElementById("tpPercent3").value = "";
          document.getElementById("slPercent").value = "";
        }
        const activeCoinPrice = currentMarketPrices[symbol];
        if (
          activeCoinPrice !== null &&
          activeCoinPrice !== undefined &&
          !isNaN(activeCoinPrice)
        ) {
          marketPriceDisplay.textContent = formatNumber(
            activeCoinPrice,
            guessDecimalPlaces(activeCoinPrice),
          );
          marketPriceDisplay.classList.remove("error");
        } else {
          marketPriceDisplay.textContent = "---";
          marketPriceDisplay.classList.add("error");
        }
        calculateActiveCoinDetails();
        updateCoinStatus();
        updateTradingViewButton(symbol);

        // Load DCA data for the selected coin
        loadDCAData();

        // تحديث شارت TradingView
        if (typeof loadTradingViewChart === 'function') {
          setTimeout(() => {
            loadTradingViewChart(symbol);
          }, 300);
        }
      }

      function clearUIFields() {
        document.getElementById("initialEntryPrice").value = "";
        document.getElementById("initialAmountDollars").value = "";
        document.getElementById("tpPercent1").value = "";
        document.getElementById("tpPercent2").value = "";
        document.getElementById("tpPercent3").value = "";
        document.getElementById("slPercent").value = "";
        for (let i = 1; i <= maxRepurchaseEntries; i++) {
          const pIn = document.getElementById(`repurchasePrice${i}`);
          if (pIn) pIn.value = "";
          const aIn = document.getElementById(`repurchaseAmount${i}`);
          if (aIn) aIn.value = "";
        }
        marketPriceDisplay.textContent = "---";
        marketPriceDisplay.classList.remove("error");
        apiStatusDiv.textContent = "اختر عملة أو أضف واحدة جديدة";
        apiStatusDiv.style.color = "var(--text-muted)";
        updateCurrentCoinDisplay("لا عملة محددة");

        // Clear DCA data
        dcaRows = [];
        dcaRowCounter = 0;
        renderDCATable();

        calculateActiveCoinDetails();
        updateTradingViewButton(null);
      }

      function updateCurrentCoinDisplay(symbol) {
        const displayText = symbol || "---";
        currentCoinDisplayElements.forEach((el) => {
          if (el) el.textContent = displayText;
        });
      }

      function updateCoinStatus() {
        const count = Object.keys(allCoinData).length;
        coinStatusDiv.textContent = `العملات: ${count}`;
        document.getElementById("deleteCoinBtn").disabled = !activeCoinSymbol;
      }

      function updateActiveCoinDataInMemory() {
        if (!activeCoinSymbol || !allCoinData[activeCoinSymbol]) return;
        const currentData = allCoinData[activeCoinSymbol];
        currentData.initialEntryPrice =
          document.getElementById("initialEntryPrice").value;
        currentData.initialAmountDollars = document.getElementById(
          "initialAmountDollars",
        ).value;
        currentData.repurchases = [];
        for (let i = 1; i <= maxRepurchaseEntries; i++) {
          currentData.repurchases.push({
            price: document.getElementById(`repurchasePrice${i}`)?.value || "",
            amount:
              document.getElementById(`repurchaseAmount${i}`)?.value || "",
          });
        }
        currentData.targets = {
          tp1: document.getElementById("tpPercent1").value,
          tp2: document.getElementById("tpPercent2").value,
          tp3: document.getElementById("tpPercent3").value,
          sl: document.getElementById("slPercent").value,
        };
      }

      function saveAndCalculate() {
        calculateActiveCoinDetails();
        if (activeCoinSymbol) {
          updateActiveCoinDataInMemory();
          saveAllDataToLocalStorage();
        }
        updateSummaryTable();
      }

      function createRepurchaseRows() {
        // This function is now replaced by renderDCATable()
        // Keep it for compatibility but make it call the new DCA system
        renderDCATable();
      }

      async function fetchSinglePrice(symbol) {
        if (!symbol || typeof symbol !== "string" || symbol.length < 4) {
          return {
            symbol: symbol,
            price: null,
            source: null,
            error: "رمز غير صالح",
          };
        }
        const binanceApiUrl = `https://api.binance.com/api/v3/ticker/price?symbol=${symbol}`;
        const kucoinSymbol = symbol.endsWith("USDT")
          ? `${symbol.slice(0, -4)}-USDT`
          : symbol;
        const kucoinApiUrl = `https://api.kucoin.com/api/v1/market/orderbook/level1?symbol=${kucoinSymbol}`;
        let price = null;
        let source = "";
        let error = null;
        try {
          const response = await fetch(binanceApiUrl);
          if (response.ok) {
            const data = await response.json();
            if (data && data.price && !isNaN(parseFloat(data.price))) {
              price = parseFloat(data.price);
              source = "Binance";
            }
          }
        } catch (e) {
          console.warn(`Binance fetch failed for ${symbol}:`, e.message);
        }
        if (price === null) {
          try {
            const kucoinResponse = await fetch(kucoinApiUrl);
            if (kucoinResponse.ok) {
              const kucoinData = await kucoinResponse.json();
              if (
                kucoinData.code === "200000" &&
                kucoinData.data &&
                kucoinData.data.price &&
                !isNaN(parseFloat(kucoinData.data.price))
              ) {
                price = parseFloat(kucoinData.data.price);
                source = "KuCoin";
              }
            }
          } catch (e) {
            console.warn(`KuCoin fetch failed for ${kucoinSymbol}:`, e.message);
          }
        }
        if (price !== null) {
          return { symbol: symbol, price: price, source: source, error: null };
        } else {
          error = "فشل جلب السعر";
          console.error(
            `Failed to fetch price for ${symbol} from both Binance and KuCoin.`,
          );
          return { symbol: symbol, price: null, source: null, error: error };
        }
      }

      async function fetchAllPrices(isAutoRefresh = false) {
        const trackedSymbols = Object.keys(allCoinData);
        if (trackedSymbols.length === 0) {
          if (!isAutoRefresh) {
            apiStatusDiv.textContent = "لا توجد عملات للمراقبة.";
            apiStatusDiv.style.color = "var(--text-muted)";
            summaryTableBody.innerHTML = `
              <tr class="hover:bg-gradient-to-r hover:from-slate-50/50 hover:to-slate-100/30 dark:hover:from-slate-100/30 dark:hover:to-slate-50/50 transition-all duration-300 group">
                <td colspan="7" class="px-8 py-16 text-center">
                  <div class="flex flex-col items-center space-y-6">
                    <div class="relative">
                      <div class="w-20 h-20 bg-gradient-to-br from-amber-200 via-amber-300 to-amber-400 dark:from-amber-200 dark:via-amber-300 dark:to-amber-400 rounded-3xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                        <i class="fas fa-plus text-3xl text-amber-600 dark:text-amber-700 group-hover:scale-110 transition-transform duration-300"></i>
                      </div>
                      <div class="absolute -inset-2 bg-gradient-to-r from-crypto-primary/20 to-crypto-secondary/20 rounded-3xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <div class="text-center space-y-3">
                      <h4 class="text-xl font-bold text-amber-700 dark:text-amber-700">لا توجد عملات مضافة</h4>
                      <p class="text-sm text-amber-600 dark:text-amber-600 max-w-md mx-auto leading-relaxed">
                        استخدم لوحة التحكم أعلاه لإضافة عملة رقمية جديدة
                      </p>
                    </div>
                  </div>
                </td>
              </tr>
            `;
            resetTotals();
          }
          return;
        }
        if (!isAutoRefresh) {
          apiStatusDiv.textContent = `جاري جلب أسعار ${trackedSymbols.length} عملة...`;
          apiStatusDiv.style.color = "var(--primary-color)";
          summaryTableBody.innerHTML = `
            <tr class="hover:bg-gradient-to-r hover:from-slate-50/50 hover:to-slate-100/30 dark:hover:from-slate-100/30 dark:hover:to-slate-50/50 transition-all duration-300 group">
              <td colspan="7" class="px-8 py-16 text-center">
                <div class="flex flex-col items-center space-y-6">
                  <div class="relative">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-200 via-blue-300 to-blue-400 dark:from-blue-200 dark:via-blue-300 dark:to-blue-400 rounded-3xl flex items-center justify-center shadow-lg animate-pulse">
                      <i class="fas fa-arrows-rotate text-3xl text-blue-600 dark:text-blue-700 animate-spin"></i>
                    </div>
                  </div>
                  <div class="text-center space-y-3">
                    <h4 class="text-xl font-bold text-blue-700 dark:text-blue-700">جاري تحديث الأسعار</h4>
                    <p class="text-sm text-blue-600 dark:text-blue-600 max-w-md mx-auto leading-relaxed">
                      يرجى الانتظار بينما نقوم بجلب أحدث أسعار العملات الرقمية...
                    </p>
                  </div>
                </div>
              </td>
            </tr>
          `;
        }
        const pricePromises = trackedSymbols.map((symbol) =>
          fetchSinglePrice(symbol),
        );
        try {
          const results = await Promise.allSettled(pricePromises);
          let successCount = 0;
          let failCount = 0;
          const fetchTimestamp = new Date();
          results.forEach((result) => {
            if (result.status === "fulfilled") {
              const { symbol, price, error } = result.value;
              if (error === null && price !== null) {
                currentMarketPrices[symbol] = price;
                successCount++;
              } else {
                if (currentMarketPrices[symbol] === undefined) {
                  currentMarketPrices[symbol] = null;
                }
                failCount++;
                console.error(
                  `Price fetch logic error for ${symbol}: ${error || "No price"}`,
                );
              }
            } else {
              failCount++;
              console.error(
                `Promise rejected for symbol fetch:`,
                result.reason,
              );
              const failedSymbol = trackedSymbols.find((s) =>
                result.reason?.message?.includes(s),
              );
              if (
                failedSymbol &&
                currentMarketPrices[failedSymbol] === undefined
              ) {
                currentMarketPrices[failedSymbol] = null;
              }
            }
          });
          if (activeCoinSymbol) {
            const price = currentMarketPrices[activeCoinSymbol];
            if (price !== null && price !== undefined && !isNaN(price)) {
              marketPriceDisplay.textContent = formatNumber(
                price,
                guessDecimalPlaces(price),
              );
              marketPriceDisplay.classList.remove("error");
            } else {
              marketPriceDisplay.textContent = "فشل الجلب";
              marketPriceDisplay.classList.add("error");
            }
          }
          calculateActiveCoinDetails();
          updateSummaryTable();
          const statusMsg = `${successCount} من أصل  ${failCount} `;
          apiStatusDiv.textContent = isAutoRefresh
            ? `تلقائي: ${statusMsg}`
            : `تم تحديث: ${statusMsg}`;
          apiStatusDiv.style.color =
            failCount > 0 ? "var(--negative-color)" : "var(--positive-color)";
          if (autoRefreshCheckbox.checked && !autoRefreshIntervalId) {
            startAutoRefresh();
          }
        } catch (error) {
          console.error("Unexpected error during fetchAllPrices:", error);
          if (!isAutoRefresh) {
            apiStatusDiv.textContent = `خطأ عام أثناء تحديث الأسعار.`;
            apiStatusDiv.style.color = "var(--negative-color)";
          }
          stopAutoRefresh();
          updateSummaryTable();
        }
      }

      function calculateSummaryData() {
        const summaryData = [];
        const coinSymbols = Object.keys(allCoinData);
        let grandTotalInvested = 0;
        let grandTotalPnlAmount = 0;
        coinSymbols.forEach((symbol) => {
          const data = allCoinData[symbol];
          if (!data) return;
          const marketPrice = currentMarketPrices[symbol];
          const initialEntryPrice = parseFloat(data.initialEntryPrice) || 0;
          const initialAmountDollars =
            parseFloat(data.initialAmountDollars) || 0;
          let totalCoinQty = 0;
          let totalInvestedAmount = 0;
          let errorMsg = null;
          if (initialEntryPrice > 0 && initialAmountDollars > 0) {
            totalCoinQty = initialAmountDollars / initialEntryPrice;
            totalInvestedAmount = initialAmountDollars;
          } else if (initialAmountDollars > 0 && initialEntryPrice <= 0) {
            errorMsg = "سعر الدخول الأولي مفقود";
          }
          // Add old repurchases system for backward compatibility
          if (data.repurchases) {
            data.repurchases.forEach((rp) => {
              const rpPrice = parseFloat(rp.price) || 0;
              const rpAmount = parseFloat(rp.amount) || 0;
              if (rpPrice > 0 && rpAmount > 0) {
                totalCoinQty += rpAmount / rpPrice;
                totalInvestedAmount += rpAmount;
              } else if (rpAmount > 0 && rpPrice <= 0) {
                if (!errorMsg) errorMsg = "سعر تعزيز مفقود";
              }
            });
          }

          // Add new DCA system calculations (only unsold rows)
          if (data.dcaRows) {
            data.dcaRows.forEach((dcaRow) => {
              if (!dcaRow.isSold) { // Only count unsold DCA rows
                const dcaPrice = parseFloat(dcaRow.dcaPrice) || 0;
                const dcaAmount = parseFloat(dcaRow.amount) || 0;
                if (dcaPrice > 0 && dcaAmount > 0) {
                  totalCoinQty += dcaAmount / dcaPrice;
                  totalInvestedAmount += dcaAmount;
                } else if (dcaAmount > 0 && dcaPrice <= 0) {
                  if (!errorMsg) errorMsg = "سعر تعزيز DCA مفقود";
                }
              }
            });
          }
          const averageEntryPrice =
            totalCoinQty > 0 ? totalInvestedAmount / totalCoinQty : 0;
          let currentPortfolioValue = 0;
          let pnlAmount = 0;
          let pnlPercent = 0;
          if (marketPrice === null || marketPrice === undefined) {
            if (!errorMsg) errorMsg = "لم يتم جلب السعر";
          } else if (totalInvestedAmount <= 0 && totalCoinQty <= 0) {
            pnlAmount = 0;
            pnlPercent = 0;
            currentPortfolioValue = 0;
          } else if (errorMsg) {
            pnlAmount = NaN;
            pnlPercent = NaN;
            currentPortfolioValue = NaN;
          } else {
            currentPortfolioValue = totalCoinQty * marketPrice;
            pnlAmount = currentPortfolioValue - totalInvestedAmount;
            pnlPercent =
              totalInvestedAmount > 0
                ? (pnlAmount / totalInvestedAmount) * 100
                : 0;
          }
          if (!isNaN(pnlAmount) && totalInvestedAmount >= 0) {
            grandTotalInvested += totalInvestedAmount;
            grandTotalPnlAmount += pnlAmount;
          }
          summaryData.push({
            symbol,
            totalCoinQty,
            totalInvestedAmount,
            averageEntryPrice,
            marketPrice,
            currentPortfolioValue,
            pnlAmount,
            pnlPercent,
            error: errorMsg,
          });
        });
        summaryData.sort((a, b) => a.symbol.localeCompare(b.symbol));
        const grandTotalCurrentValue = grandTotalInvested + grandTotalPnlAmount;
        const grandTotalPnlPercent =
          grandTotalInvested > 0
            ? (grandTotalPnlAmount / grandTotalInvested) * 100
            : 0;
        return {
          summaryRows: summaryData,
          totals: {
            invested: grandTotalInvested,
            pnlAmount: grandTotalPnlAmount,
            currentValue: grandTotalCurrentValue,
            pnlPercent: grandTotalPnlPercent,
          },
        };
      }

      function updateSummaryTable() {
        const { summaryRows, totals } = calculateSummaryData();

        // استخدام DocumentFragment لتحسين الأداء
        const fragment = document.createDocumentFragment();

        if (summaryRows.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.className = "hover:bg-gradient-to-r hover:from-slate-50/50 hover:to-slate-100/30 dark:hover:from-slate-100/30 dark:hover:to-slate-50/50 transition-all duration-300 group";
          emptyRow.innerHTML = `
            <td colspan="7" class="px-8 py-16 text-center">
              <div class="flex flex-col items-center space-y-6">
                <div class="relative">
                  <div class="w-20 h-20 bg-gradient-to-br from-slate-200 via-slate-300 to-slate-400 dark:from-slate-200 dark:via-slate-300 dark:to-slate-400 rounded-3xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                    <i class="fas fa-chart-line text-3xl text-slate-500 dark:text-slate-600 group-hover:scale-110 transition-transform duration-300"></i>
                  </div>
                  <div class="absolute -inset-2 bg-gradient-to-r from-crypto-primary/20 to-crypto-secondary/20 rounded-3xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div class="text-center space-y-3">
                  <h4 class="text-xl font-bold text-slate-700 dark:text-slate-700">لا توجد بيانات للعرض</h4>
                  <p class="text-sm text-slate-600 dark:text-slate-600 max-w-md mx-auto leading-relaxed">
                    ابدأ بإضافة عملة رقمية جديدة أو قم بتحديث الأسعار لعرض تفاصيل محفظتك الاستثمارية
                  </p>
                </div>
              </div>
            </td>
          `;

          summaryTableBody.innerHTML = '';
          summaryTableBody.appendChild(emptyRow);
          resetTotals();
          return;
        }
        // إنشاء الصفوف باستخدام DocumentFragment
        summaryTableBody.innerHTML = '';

        summaryRows.forEach((item) => {
          const row = document.createElement("tr");
          const pnlAmountValid = !isNaN(item.pnlAmount);
          const pnlPercentValid = !isNaN(item.pnlPercent);
          const marketPriceValid =
            item.marketPrice !== null && !isNaN(item.marketPrice);
          const avgPriceValid =
            !isNaN(item.averageEntryPrice) && item.averageEntryPrice > 0;
          const portfolioValueValid = !isNaN(item.currentPortfolioValue);
          const pnlAmountClass = pnlAmountValid
            ? item.pnlAmount > 0
              ? "pnl-positive"
              : item.pnlAmount < 0
                ? "pnl-negative"
                : ""
            : "";
          const pnlPercentClass = pnlPercentValid
            ? item.pnlPercent > 0
              ? "pnl-positive"
              : item.pnlPercent < 0
                ? "pnl-negative"
                : ""
            : "";
          const displayPrice = marketPriceValid
            ? formatNumber(
                item.marketPrice,
                guessDecimalPlaces(item.marketPrice),
              )
            : `<span class="error">${item.error || "لا يوجد سعر"}</span>`;
          const displayAvgPrice = avgPriceValid
            ? formatNumber(
                item.averageEntryPrice,
                guessDecimalPlaces(item.averageEntryPrice),
              )
            : item.totalInvestedAmount > 0
              ? '<span class="error">خطأ</span>'
              : "0.00";
          const displayPortfolioValue =
            marketPriceValid && portfolioValueValid
              ? formatNumber(item.currentPortfolioValue, 2)
              : item.error
                ? "-"
                : "0.00";
          const displayPnlAmount =
            marketPriceValid && pnlAmountValid
              ? formatNumber(item.pnlAmount, 2)
              : item.error
                ? "-"
                : "0.00";
          const displayPnlPercent =
            marketPriceValid && pnlPercentValid && item.totalInvestedAmount > 0
              ? `${formatNumber(item.pnlPercent, 2)}%`
              : item.error
                ? "-"
                : "0.00%";
          const displayQuantity = !isNaN(item.totalCoinQty)
            ? formatNumber(item.totalCoinQty, 4)
            : '<span class="error">خطأ</span>';
          const displayInvested = !isNaN(item.totalInvestedAmount)
            ? formatNumber(item.totalInvestedAmount, 2)
            : '<span class="error">خطأ</span>';
          row.className = "hover:bg-gradient-to-r hover:from-slate-50/50 hover:to-slate-100/30 dark:hover:from-slate-100/30 dark:hover:to-slate-50/50 transition-all duration-300 group border-b border-slate-200/50 dark:border-slate-300/50";

          row.innerHTML = `
  <td class="px-1 sm:px-2 lg:px-6 py-4 lg:py-6 text-center">
    <div class="bg-slate-50 dark:bg-slate-100 px-1 sm:px-2 lg:px-3 py-1 lg:py-2 rounded-lg lg:rounded-xl">
      <div class="flex flex-col items-center space-y-1">
        <span class="font-bold text-sm sm:text-base text-slate-800 dark:text-slate-800">${item.symbol}</span>
        <span class="font-mono text-xs sm:text-sm font-semibold text-slate-600 dark:text-slate-600 block truncate">${displayQuantity}</span>
      </div>
    </div>
  </td>
  <td class="px-1 sm:px-2 lg:px-6 py-4 lg:py-6 text-center">
    <div class="bg-blue-50 dark:bg-blue-100 px-1 sm:px-2 lg:px-3 py-1 lg:py-2 rounded-lg lg:rounded-xl border border-blue-200/50">
      <span class="font-mono text-xs sm:text-sm font-bold text-blue-800 dark:text-blue-800 block truncate">$${displayInvested}</span>
    </div>
  </td>
  <td class="px-1 sm:px-2 lg:px-6 py-4 lg:py-6 text-center">
    <div class="bg-purple-50 dark:bg-purple-100 px-1 sm:px-2 lg:px-3 py-1 lg:py-2 rounded-lg lg:rounded-xl border border-purple-200/50">
      <span class="font-mono text-xs sm:text-sm font-bold text-purple-800 dark:text-purple-800 block truncate">${displayAvgPrice.includes('$') ? displayAvgPrice : '$' + displayAvgPrice}</span>
    </div>
  </td>
  <td class="px-1 sm:px-2 lg:px-6 py-4 lg:py-6 text-center">
    <div class="bg-indigo-50 dark:bg-indigo-100 px-1 sm:px-2 lg:px-3 py-1 lg:py-2 rounded-lg lg:rounded-xl border border-indigo-200/50">
      <span class="font-mono text-xs sm:text-sm font-bold text-indigo-800 dark:text-indigo-800 block truncate">${displayPrice.includes('$') || displayPrice.includes('<span') ? displayPrice : '$' + displayPrice}</span>
    </div>
  </td>
  <td class="px-1 sm:px-2 lg:px-6 py-4 lg:py-6 text-center">
    <div class="bg-emerald-50 dark:bg-emerald-100 px-1 sm:px-2 lg:px-3 py-1 lg:py-2 rounded-lg lg:rounded-xl border border-emerald-200/50">
      <span class="font-mono text-xs sm:text-sm font-bold text-emerald-800 dark:text-emerald-800 block truncate">${displayPortfolioValue.includes('$') ? displayPortfolioValue : '$' + displayPortfolioValue}</span>
    </div>
  </td>
  <td class="px-1 sm:px-2 lg:px-6 py-4 lg:py-6 text-center">
    <div class="px-1 sm:px-2 lg:px-3 py-1 lg:py-2 rounded-lg lg:rounded-xl border ${pnlAmountClass === 'pnl-positive' ? 'bg-green-50 dark:bg-green-100 border-green-200/50' : pnlAmountClass === 'pnl-negative' ? 'bg-red-50 dark:bg-red-100 border-red-200/50' : 'bg-slate-50 dark:bg-slate-100 border-slate-200/50'}">
      <span class="font-mono text-xs sm:text-sm font-bold ${pnlAmountClass === 'pnl-positive' ? 'text-green-800 dark:text-green-800' : pnlAmountClass === 'pnl-negative' ? 'text-red-800 dark:text-red-800' : 'text-slate-800 dark:text-slate-800'} block truncate">${displayPnlAmount.includes('$') ? displayPnlAmount : '$' + displayPnlAmount}</span>
    </div>
  </td>
  <td class="px-1 sm:px-2 lg:px-6 py-4 lg:py-6 text-center">
    <div class="px-1 sm:px-2 lg:px-3 py-1 lg:py-2 rounded-lg lg:rounded-xl border ${pnlPercentClass === 'pnl-positive' ? 'bg-green-50 dark:bg-green-100 border-green-200/50' : pnlPercentClass === 'pnl-negative' ? 'bg-red-50 dark:bg-red-100 border-red-200/50' : 'bg-slate-50 dark:bg-slate-100 border-slate-200/50'}">
      <span class="font-mono text-xs sm:text-sm font-bold ${pnlPercentClass === 'pnl-positive' ? 'text-green-800 dark:text-green-800' : pnlPercentClass === 'pnl-negative' ? 'text-red-800 dark:text-red-800' : 'text-slate-800 dark:text-slate-800'} block truncate">${displayPnlPercent}</span>
    </div>
  </td>
`;
          fragment.appendChild(row);
        });

        // إضافة جميع الصفوف دفعة واحدة لتحسين الأداء
        summaryTableBody.appendChild(fragment);

        // تحديث الإجماليات
        totalInvestedSummaryEl.textContent = `${formatNumber(totals.invested, 2)} $`;
        totalPnlAmountSummaryEl.textContent = `${formatNumber(totals.pnlAmount, 2)} $`;
        totalCurrentValueSummaryEl.textContent = `${formatNumber(totals.currentValue, 2)} $`;
        totalPnlPercentSummaryEl.textContent = `${formatNumber(totals.pnlPercent, 2)} %`;
        totalPnlAmountSummaryEl.className =
          totals.pnlAmount > 0
            ? "totals-positive"
            : totals.pnlAmount < 0
              ? "totals-negative"
              : "";
        totalPnlPercentSummaryEl.className =
          totals.pnlPercent > 0
            ? "totals-positive"
            : totals.pnlPercent < 0
              ? "totals-negative"
              : "";
        totalCurrentValueSummaryEl.className =
          totals.pnlAmount >= 0 ? "totals-positive" : "totals-negative";
      }

      function resetTotals() {
        totalInvestedSummaryEl.textContent = `0.00 $`;
        totalPnlAmountSummaryEl.textContent = `0.00 $`;
        totalCurrentValueSummaryEl.textContent = `0.00 $`;
        totalPnlPercentSummaryEl.textContent = `0.00 %`;
        totalPnlAmountSummaryEl.className = "";
        totalPnlPercentSummaryEl.className = "";
        totalCurrentValueSummaryEl.className = "";
      }

      function calculateActiveCoinDetails() {
        document.getElementById("initialCoinQty").textContent = formatNumber(
          0,
          8,
        );
        document.getElementById("totalCoinQty").textContent = formatNumber(
          0,
          8,
        );
        document.getElementById("totalInvestedAmount").textContent =
          formatNumber(0, 2);
        document.getElementById("averageEntryPrice").textContent = formatNumber(
          0,
          8,
        );
        document.getElementById("currentPortfolioValue").textContent =
          formatNumber(0, 2);
        const pnlAmountElement = document.getElementById("pnlAmount");
        const pnlPercentElement = document.getElementById("pnlPercent");
        pnlAmountElement.textContent = formatNumber(0, 2);
        pnlPercentElement.textContent = formatNumber(0, 2) + "%";
        pnlAmountElement.className = "value pnl-neutral";
        pnlPercentElement.className = "value pnl-neutral";
        document.getElementById("tpPrice1").textContent = formatNumber(0, 8);
        document.getElementById("tpPrice2").textContent = formatNumber(0, 8);
        document.getElementById("tpPrice3").textContent = formatNumber(0, 8);
        document.getElementById("slPrice").textContent = formatNumber(0, 8);

        for (let i = 1; i <= maxRepurchaseEntries; i++) {
          const dpSpan = document.getElementById(`downPercent${i}`);
          const rpQtyDiv = document.getElementById(`repurchaseQty${i}`);
          const rpPnlDiv = document.getElementById(`repurchasePnl${i}`);
          const rpPnlPercentDiv = document.getElementById(
            `repurchasePnlPercent${i}`,
          );
          if (dpSpan) {
            dpSpan.textContent = "-%";
            dpSpan.className = "down-percent";
          }
          if (rpQtyDiv) rpQtyDiv.textContent = formatNumber(0, 8);
          if (rpPnlDiv) {
            rpPnlDiv.textContent = formatNumber(0, 2);
            rpPnlDiv.className = "output-field repurchase-pnl";
          }
          if (rpPnlPercentDiv) {
            rpPnlPercentDiv.textContent = formatNumber(0, 2) + "%";
            rpPnlPercentDiv.className = "output-field repurchase-pnl-percent";
          }
        }
        if (!activeCoinSymbol || !allCoinData[activeCoinSymbol]) return;

        const data = allCoinData[activeCoinSymbol];
        const marketPrice = currentMarketPrices[activeCoinSymbol] || 0;
        const initialEntryPrice = parseFloat(data.initialEntryPrice) || 0;
        const initialAmountDollars = parseFloat(data.initialAmountDollars) || 0;
        const initialCoinQty =
          initialEntryPrice > 0 ? initialAmountDollars / initialEntryPrice : 0;
        document.getElementById("initialCoinQty").textContent = formatNumber(
          initialCoinQty,
          8,
        );
        let totalCoinQty = initialCoinQty;
        let totalInvestedAmount = initialAmountDollars;

        // Add DCA calculations (only unsold rows)
        if (data.dcaRows) {
          data.dcaRows.forEach((dcaRow) => {
            if (!dcaRow.isSold) { // Only count unsold DCA rows
              const dcaPrice = parseFloat(dcaRow.dcaPrice) || 0;
              const dcaAmount = parseFloat(dcaRow.amount) || 0;
              if (dcaPrice > 0 && dcaAmount > 0) {
                const dcaQty = dcaAmount / dcaPrice;
                totalCoinQty += dcaQty;
                totalInvestedAmount += dcaAmount;
              }
            }
          });
        }

        for (let i = 1; i <= maxRepurchaseEntries; i++) {
          const rpPriceInput = document.getElementById(`repurchasePrice${i}`);
          const rpAmountInput = document.getElementById(`repurchaseAmount${i}`);
          const repurchasePrice = parseFloat(rpPriceInput?.value) || 0;
          const repurchaseAmount = parseFloat(rpAmountInput?.value) || 0;
          let changePercent = 0;
          let repurchaseQty = 0;
          let pnlForThisRepurchase = 0;
          let pnlPercentForThisRepurchase = 0;

          if (repurchasePrice > 0 && repurchaseAmount > 0) {
            if (initialEntryPrice > 0) {
              changePercent =
                ((repurchasePrice - initialEntryPrice) / initialEntryPrice) *
                100;
            }
            repurchaseQty = repurchaseAmount / repurchasePrice;
            totalCoinQty += repurchaseQty;
            totalInvestedAmount += repurchaseAmount;
            if (marketPrice > 0) {
              const currentValueOfRepurchase = repurchaseQty * marketPrice;
              pnlForThisRepurchase =
                currentValueOfRepurchase - repurchaseAmount;
              pnlPercentForThisRepurchase =
                repurchaseAmount > 0
                  ? (pnlForThisRepurchase / repurchaseAmount) * 100
                  : 0;
            }
          }
          const dpSpan = document.getElementById(`downPercent${i}`);
          const rpQtyDiv = document.getElementById(`repurchaseQty${i}`);
          const rpPnlDiv = document.getElementById(`repurchasePnl${i}`);
          const rpPnlPercentDiv = document.getElementById(
            `repurchasePnlPercent${i}`,
          );

          if (dpSpan) {
            dpSpan.textContent =
              changePercent !== 0 && isFinite(changePercent)
                ? `${formatNumber(changePercent, 2)}%`
                : "-%";
            dpSpan.className = "down-percent";
            if (changePercent < 0) dpSpan.classList.add("negative");
            else if (changePercent > 0) dpSpan.classList.add("positive");
          }
          if (rpQtyDiv) rpQtyDiv.textContent = formatNumber(repurchaseQty, 8);
          if (rpPnlDiv) {
            rpPnlDiv.textContent = formatNumber(pnlForThisRepurchase, 2);
            rpPnlDiv.className = "output-field repurchase-pnl";
            if (pnlForThisRepurchase > 0)
              rpPnlDiv.classList.add("pnl-positive");
            else if (pnlForThisRepurchase < 0)
              rpPnlDiv.classList.add("pnl-negative");
          }
          if (rpPnlPercentDiv) {
            rpPnlPercentDiv.textContent = `${formatNumber(pnlPercentForThisRepurchase, 2)}%`;
            rpPnlPercentDiv.className = "output-field repurchase-pnl-percent";
            if (pnlPercentForThisRepurchase > 0)
              rpPnlPercentDiv.classList.add("pnl-positive");
            else if (pnlPercentForThisRepurchase < 0)
              rpPnlPercentDiv.classList.add("pnl-negative");
          }
        }

        const averageEntryPrice =
          totalCoinQty > 0 ? totalInvestedAmount / totalCoinQty : 0;
        const currentPortfolioValue = totalCoinQty * marketPrice;
        const pnlAmount =
          totalInvestedAmount > 0 ||
          currentPortfolioValue > 0 ||
          totalCoinQty > 0
            ? currentPortfolioValue - totalInvestedAmount
            : 0;
        const pnlPercent =
          totalInvestedAmount > 0 ? (pnlAmount / totalInvestedAmount) * 100 : 0;
        document.getElementById("totalCoinQty").textContent = formatNumber(
          totalCoinQty,
          8,
        );
        document.getElementById("totalInvestedAmount").textContent =
          formatNumber(totalInvestedAmount, 2);
        document.getElementById("averageEntryPrice").textContent = formatNumber(
          averageEntryPrice,
          guessDecimalPlaces(averageEntryPrice),
        );
        document.getElementById("currentPortfolioValue").textContent =
          formatNumber(currentPortfolioValue, 2);
        pnlAmountElement.textContent = formatNumber(pnlAmount, 2);
        pnlPercentElement.textContent = `${formatNumber(pnlPercent, 2)}%`;
        pnlAmountElement.className = "value";
        pnlPercentElement.className = "value";
        if (pnlAmount > 0) {
          pnlAmountElement.classList.add("pnl-positive");
          pnlPercentElement.classList.add("pnl-positive");
        } else if (pnlAmount < 0) {
          pnlAmountElement.classList.add("pnl-negative");
          pnlPercentElement.classList.add("pnl-negative");
        } else {
          pnlAmountElement.classList.add("pnl-neutral");
          pnlPercentElement.classList.add("pnl-neutral");
        }

        const tpP1 =
            parseFloat(document.getElementById("tpPercent1").value) || 0,
          tpP2 = parseFloat(document.getElementById("tpPercent2").value) || 0;
        const tpP3 =
            parseFloat(document.getElementById("tpPercent3").value) || 0,
          slP = parseFloat(document.getElementById("slPercent").value) || 0;
        const avgPriceDecimals = guessDecimalPlaces(averageEntryPrice);
        const tpPrice1 =
          averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP1 / 100) : 0;
        const tpPrice2 =
          averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP2 / 100) : 0;
        const tpPrice3 =
          averageEntryPrice > 0 ? averageEntryPrice * (1 + tpP3 / 100) : 0;
        const slPriceVal =
          averageEntryPrice > 0 && slP > 0
            ? averageEntryPrice * (1 - slP / 100)
            : 0;
        document.getElementById("tpPrice1").textContent = formatNumber(
          tpPrice1,
          avgPriceDecimals,
        );
        document.getElementById("tpPrice2").textContent = formatNumber(
          tpPrice2,
          avgPriceDecimals,
        );
        document.getElementById("tpPrice3").textContent = formatNumber(
          tpPrice3,
          avgPriceDecimals,
        );
        document.getElementById("slPrice").textContent = formatNumber(
          slPriceVal,
          avgPriceDecimals,
        );

        // Update remaining quantity display
        updateRemainingQuantity();
      }

      function formatNumber(num, decimalPlaces = 8) {
        const number = Number(num);
        if (isNaN(number) || !isFinite(number))
          return (0).toFixed(decimalPlaces);
        return number.toFixed(decimalPlaces);
      }
      function guessDecimalPlaces(price) {
        const num = Number(price);
        if (isNaN(num) || num === 0) return 2;
        if (num >= 1000) return 2;
        if (num >= 10) return 4;
        if (num >= 0.1) return 5;
        if (num >= 0.001) return 6;
        if (num >= 0.0001) return 7;
        return 8;
      }



      function startAutoRefresh() {
        if (autoRefreshIntervalId) clearInterval(autoRefreshIntervalId);
        const trackedSymbols = Object.keys(allCoinData);
        if (trackedSymbols.length > 0 && autoRefreshCheckbox.checked) {
          if (!apiStatusDiv.textContent.includes("(تلقائي مفعل)"))
            apiStatusDiv.textContent += " (تلقائي مفعل)";
          autoRefreshIntervalId = setInterval(
            () => fetchAllPrices(true),
            AUTO_REFRESH_INTERVAL,
          );
        } else {
          autoRefreshCheckbox.checked = false;
          if (trackedSymbols.length === 0) {
            apiStatusDiv.textContent = "أضف عملة للتحديث التلقائي.";
            apiStatusDiv.style.color = "var(--text-muted)";
          }
        }
      }
      function stopAutoRefresh() {
        if (autoRefreshIntervalId) {
          clearInterval(autoRefreshIntervalId);
          autoRefreshIntervalId = null;
          let currentStatus = apiStatusDiv.textContent;
          currentStatus = currentStatus
            .replace(/\(تلقائي مفعل\)|\(تلقائي متوقف\)/g, "")
            .trim();
          if (currentStatus.startsWith("تلقائي:")) {
            const timePartIndex = currentStatus.indexOf("(");
            currentStatus =
              timePartIndex > -1
                ? currentStatus
                    .substring(currentStatus.indexOf(":") + 1, timePartIndex)
                    .trim()
                : currentStatus
                    .substring(currentStatus.indexOf(":") + 1)
                    .trim();
          }
          apiStatusDiv.textContent = currentStatus + " (تلقائي متوقف)";
        }
      }
      function handleAutoRefreshToggle() {
        if (autoRefreshCheckbox.checked) {
          if (Object.keys(allCoinData).length > 0) {
            fetchAllPrices(false);
            startAutoRefresh();
          } else {
            autoRefreshCheckbox.checked = false;
            alert("يرجى إضافة عملة أولاً لتفعيل التحديث التلقائي.");
            stopAutoRefresh();
          }
        } else {
          stopAutoRefresh();
        }
        localStorage.setItem(AUTO_REFRESH_CHECKBOX_STATE_KEY, autoRefreshCheckbox.checked ? 'true' : 'false');
      }

      function handleCoinSelectionChange() {
        const selectedSymbol = coinSelector.value;
        if (selectedSymbol && allCoinData[selectedSymbol]) {
          activeCoinSymbol = selectedSymbol;
          newCoinNameInput.value = "";
          displayCoinData(selectedSymbol);
          saveAllDataToLocalStorage();
        } else if (!selectedSymbol) {
          activeCoinSymbol = null;
          clearUIFields();
          saveAllDataToLocalStorage();
          updateCoinStatus();
        } else {
          console.error(`Selected symbol ${selectedSymbol} not in data.`);
          activeCoinSymbol = null;
          clearUIFields();
          saveAllDataToLocalStorage();
          updateCoinStatus();
        }
      }

      function addOrSwitchCoin() {
        const symbol = newCoinNameInput.value.trim().toUpperCase();
        if (!symbol) {
          showNotification("يرجى إدخال رمز العملة (مثل BTCUSDT).", "error");
          return;
        }
        if (!/^[A-Z0-9]{3,15}$/.test(symbol)) {
          showNotification(`رمز العملة "${symbol}" غير صالح.`, "error");
          return;
        }
        if (allCoinData[symbol]) {
          coinSelector.value = symbol;
          activeCoinSymbol = symbol;
          displayCoinData(symbol);
          saveAllDataToLocalStorage();
          newCoinNameInput.value = "";
          apiStatusDiv.textContent = `تم التبديل إلى ${symbol}.`;
          apiStatusDiv.style.color = "var(--text-muted)";
        } else {
          allCoinData[symbol] = getDefaultCoinDataStructure();
          currentMarketPrices[symbol] = null;
          activeCoinSymbol = symbol;
          updateCoinSelector();
          newCoinNameInput.value = "";
          saveAllDataToLocalStorage();
          updateCoinStatus();
          updateSummaryTable();
          alert(`تمت إضافة ${symbol}. قم بتحديث الأسعار وأدخل التفاصيل.`);
          apiStatusDiv.textContent = `تمت إضافة ${symbol}.`;
          apiStatusDiv.style.color = "var(--positive-color)";
          fetchSinglePrice(symbol).then((result) => {
            if (result.price !== null) {
              currentMarketPrices[symbol] = result.price;
              if (activeCoinSymbol === symbol) displayCoinData(symbol);
              updateSummaryTable();
              apiStatusDiv.textContent = `تم جلب سعر ${symbol}.`;
              apiStatusDiv.style.color = "var(--positive-color)";
            } else {
              apiStatusDiv.textContent = `فشل جلب سعر ${symbol} (${result.error}).`;
              apiStatusDiv.style.color = "var(--negative-color)";
            }
          });
        }
      }

      function showNotification(message, type = "info") {
  // إزالة أي إشعارات سابقة
  const existingNotifications = document.querySelectorAll('.notification-toast');
  existingNotifications.forEach(notification => {
    notification.remove();
  });

  // إنشاء عنصر الإشعار
  const notification = document.createElement('div');
  notification.className = `notification-toast ${type}`;

  // تحديد الأيقونة حسب نوع الإشعار
  let icon = '🔔';
  if (type === 'success') icon = '✅';
  if (type === 'error') icon = '❌';
  if (type === 'warning') icon = '⚠️';

  // إضافة المحتوى
  notification.innerHTML = `
    <div class="notification-icon">${icon}</div>
    <div class="notification-message">${message}</div>
    <button class="notification-close" onclick="this.parentElement.remove()">×</button>
  `;

  // إضافة الإشعار للصفحة
  document.body.appendChild(notification);

  // إخفاء الإشعار تلقائيًا بعد 5 ثوانٍ
  setTimeout(() => {
    notification.classList.add('hide');
    setTimeout(() => notification.remove(), 500);
  }, 5000);
}

function deleteCurrentCoin() {
  if (!activeCoinSymbol) {
    showNotification("لا توجد عملة محددة لحذفها.", "error");
    return;
  }
  const symbolToDelete = activeCoinSymbol;
  showCustomConfirm(
    `هل أنت متأكد أنك تريد حذف بيانات العملة ${symbolToDelete}؟`,
    function () {
      delete allCoinData[symbolToDelete];
      delete currentMarketPrices[symbolToDelete];
      activeCoinSymbol = null;
      updateCoinSelector();
      saveAllDataToLocalStorage();
      if (!coinSelector.value) clearUIFields();
      updateSummaryTable();
      updateCoinStatus();
      showNotification(`تم حذف العملة ${symbolToDelete} بنجاح`, "success");
      apiStatusDiv.style.color = "var(--text-muted)";
    },
    function () {
      // إلغاء: لا شيء
    }
  );
}

function deleteAllCoins() {
  if (!allCoinData || Object.keys(allCoinData).length === 0) {
    showNotification("لا توجد عملات للحذف", "info");
    return;
  }

  const coinCount = Object.keys(allCoinData).length;
  const coinNames = Object.keys(allCoinData).join(', ');

  // نافذة تأكيد مع تفاصيل العملات
  const confirmMessage = `هل أنت متأكد من حذف جميع العملات؟\n\nسيتم حذف ${coinCount} عملة:\n${coinNames}\n\nهذا الإجراء لا يمكن التراجع عنه!`;

  showCustomConfirm(
    confirmMessage,
    function () {
      // إنشاء مؤشر تقدم للحذف
      const deleteProgress = document.createElement('div');
      deleteProgress.className = 'import-progress';
      deleteProgress.innerHTML = `
        <div class="flex items-center justify-center mb-4">
          <i class="fas fa-trash-alt text-2xl text-red-600"></i>
          <span class="import-spinner"></span>
        </div>
        <h3 class="text-lg font-bold text-slate-800 mb-2">جاري حذف جميع العملات</h3>
        <p class="text-sm text-slate-600 mb-4">يرجى الانتظار...</p>
        <div class="import-progress-bar">
          <div class="import-progress-fill" id="deleteProgressFill"></div>
        </div>
        <p class="text-xs text-slate-500" id="deleteProgressText">تحضير عملية الحذف...</p>
      `;
      document.body.appendChild(deleteProgress);

      const progressFill = document.getElementById('deleteProgressFill');
      const progressText = document.getElementById('deleteProgressText');

      // محاكاة عملية الحذف مع مؤشر التقدم
      setTimeout(() => {
        progressFill.style.width = '25%';
        progressText.textContent = 'حذف بيانات العملات...';

        setTimeout(() => {
          progressFill.style.width = '50%';
          progressText.textContent = 'مسح البيانات المحفوظة...';

          setTimeout(() => {
            progressFill.style.width = '75%';
            progressText.textContent = 'تحديث الواجهة...';

            // تنفيذ عملية الحذف الفعلية
            allCoinData = {};
            currentMarketPrices = {};
            activeCoinSymbol = null;

            // مسح بيانات DCA
            dcaRows = [];
            dcaRowCounter = 0;

            saveAllDataToLocalStorage();

            setTimeout(() => {
              progressFill.style.width = '100%';
              progressText.textContent = `تم حذف ${coinCount} عملة بنجاح!`;

              // تحديث الواجهة
              updateCoinSelector();
              clearUIFields();
              updateSummaryTable();
              updateCoinStatus();

              setTimeout(() => {
                // إزالة مؤشر التقدم
                document.body.removeChild(deleteProgress);
                showNotification(`تم حذف جميع العملات (${coinCount} عملة) بنجاح`, "success");
                apiStatusDiv.textContent = "تم حذف جميع البيانات";
                apiStatusDiv.style.color = "var(--text-muted)";
              }, 1000);
            }, 300);
          }, 300);
        }, 300);
      }, 300);
    },
    function () {
      // إلغاء: لا شيء
    }
  );
}


/**
 * دالة لتحديث نص وحالة زر شارت TradingView.
 * @param {string | null} coinSymbol - رمز العملة (مثال: "BTCUSDT") أو null.
 */
function updateTradingViewButton(coinSymbol) {
    const btn = document.getElementById('tradingViewChartBtn');
    if (!btn) {
        return;
    }

    if (coinSymbol && coinSymbol.trim() !== "") {
        let tradingViewSymbol = coinSymbol.replace('/', '');
        tradingViewSymbol = tradingViewSymbol.toUpperCase();

        btn.textContent = `شارت ${tradingViewSymbol}`;
        btn.disabled = false;
        btn.dataset.symbol = tradingViewSymbol;
    } else {
        btn.textContent = 'شارت (اختر عملة)';
        btn.disabled = true;
        btn.dataset.symbol = '';
    }
}

// دالة عرض المودال الاحترافي بدلاً من confirm
function showCustomConfirm(message, onOk, onCancel) {
  document.getElementById('customModalBody').textContent = message;
  document.getElementById('customModal').style.display = 'flex';
  document.getElementById('customModalCancelBtn').style.display = onCancel ? '' : 'none';

  // إزالة أي أحداث سابقة
  const okBtn = document.getElementById('customModalOkBtn');
  const cancelBtn = document.getElementById('customModalCancelBtn');
  okBtn.onclick = function() {
    closeCustomModal();
    if (onOk) onOk();
  };
  cancelBtn.onclick = function() {
    closeCustomModal();
    if (onCancel) onCancel();
  };
}
function closeCustomModal() {
  document.getElementById('customModal').style.display = 'none';
}


      document.addEventListener("DOMContentLoaded", () => {
        if (autoRefreshCheckbox) {
            const savedAutoRefreshState = localStorage.getItem(AUTO_REFRESH_CHECKBOX_STATE_KEY);
            if (savedAutoRefreshState === 'true') {
                autoRefreshCheckbox.checked = true;
            } else {
                autoRefreshCheckbox.checked = false;
                localStorage.setItem(AUTO_REFRESH_CHECKBOX_STATE_KEY, 'false');
            }
        }

        createRepurchaseRows();
        loadAllDataFromLocalStorage();
        updateCoinSelector();
        fetchAllPrices().then(() => {
          if (activeCoinSymbol) displayCoinData(activeCoinSymbol);
          else if (coinSelector.value && allCoinData[coinSelector.value]) {
            handleCoinSelectionChange();
          } else clearUIFields();
          if (autoRefreshCheckbox.checked) startAutoRefresh();

          // تحميل شارت TradingView بعد تحميل البيانات
          if (activeCoinSymbol && typeof loadTradingViewChart === 'function') {
            setTimeout(() => {
              loadTradingViewChart(activeCoinSymbol);
            }, 1000);
          }
        });

        const inputsToSave = [
          "initialEntryPrice",
          "initialAmountDollars",
          "tpPercent1",
          "tpPercent2",
          "tpPercent3",
          "slPercent",
        ];
        inputsToSave.forEach((id) => {
          const el = document.getElementById(id);
          if (el) el.addEventListener("input", saveAndCalculate);
        });
        repurchaseTableBody.addEventListener("input", (event) => {
          if (
            event.target &&
            (event.target.id.startsWith("repurchasePrice") ||
              event.target.id.startsWith("repurchaseAmount"))
          ) {
            saveAndCalculate();
          }
        });
        autoRefreshCheckbox.addEventListener("change", handleAutoRefreshToggle);
        newCoinNameInput.addEventListener("keypress", (e) => {
          if (e.key === "Enter") {
            e.preventDefault();
            addOrSwitchCoin();
          }
        });
        coinSelector.addEventListener("change", () => {
          if (coinSelector.value) newCoinNameInput.value = "";
          handleCoinSelectionChange();
        });



    const tradingViewBtn = document.getElementById('tradingViewChartBtn');
    if (tradingViewBtn) {
        tradingViewBtn.addEventListener('click', function() {
            if (this.disabled) return;
            const symbol = this.dataset.symbol;
            if (symbol) {
                const url = `https://ar.tradingview.com/chart/?symbol=BINANCE:${symbol}`;
                window.open(url, '_blank');
            }
        });
    }

    const coinSelectorForTVButton = document.getElementById('coinSelector');
    if (coinSelectorForTVButton) {
        updateTradingViewButton(coinSelectorForTVButton.value);
    } else {
        updateTradingViewButton(null);
    }
      });

      function downloadAllDataAsTxt() {
        if (!allCoinData || Object.keys(allCoinData).length === 0) {
          showNotification("لا توجد بيانات لحفظها.", "error");
          return;
        }

        let content = "";
        for (const [symbol, data] of Object.entries(allCoinData)) {
          content += `رمز: ${symbol}\n`;
          content += `سعر الدخول: ${data.initialEntryPrice || 0}\n`;
          content += `المبلغ: ${data.initialAmountDollars || 0}\n`;

          data.repurchases.forEach((rp, i) => {
            if (rp.price && rp.amount) {
              content += `تعزيز ${i + 1}: ${rp.price}, ${rp.amount}\n`;
            }
          });

          content += `هدف1: ${data.targets.tp1 || 0}\n`;
          content += `هدف2: ${data.targets.tp2 || 0}\n`;
          content += `هدف3: ${data.targets.tp3 || 0}\n`;
          content += `وقف: ${data.targets.sl || 0}\n`;
          content += `\n`;
        }

        const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
        const link = document.createElement("a");
        link.download = "بيانات_العملات.txt";
        link.href = URL.createObjectURL(blob);
        link.click();
      }

      function handleImportTxtFile(event) {
        const file = event.target.files[0];
        if (!file) return;

        // إنشاء مؤشر التقدم البصري
        const progressModal = document.createElement('div');
        progressModal.className = 'import-progress';
        progressModal.innerHTML = `
          <div class="flex items-center justify-center mb-4">
            <i class="fas fa-file-import text-2xl text-blue-600"></i>
            <span class="import-spinner"></span>
          </div>
          <h3 class="text-lg font-bold text-slate-800 mb-2">جاري استيراد البيانات</h3>
          <p class="text-sm text-slate-600 mb-4">يرجى الانتظار...</p>
          <div class="import-progress-bar">
            <div class="import-progress-fill" id="importProgressFill"></div>
          </div>
          <p class="text-xs text-slate-500" id="importProgressText">تحضير البيانات...</p>
        `;
        document.body.appendChild(progressModal);

        // تعطيل زر الاستيراد مؤقتاً
        const importBtn = document.querySelector('button[onclick*="importTxtFile"]');
        if (importBtn) {
          importBtn.disabled = true;
          importBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i><span class="text-sm font-medium">جاري الاستيراد...</span>';
        }

        const reader = new FileReader();
        reader.onload = function (e) {
          // استخدام setTimeout لجعل العملية غير متزامنة
          setTimeout(() => {
            try {
              const progressFill = document.getElementById('importProgressFill');
              const progressText = document.getElementById('importProgressText');

              // تحديث التقدم: قراءة الملف
              progressFill.style.width = '20%';
              progressText.textContent = 'قراءة الملف...';

              const lines = e.target.result.split("\n");
              let symbol = null;
              let coinData = getDefaultCoinDataStructure();
              let repIndex = 0;
              let importedCoins = 0;
              let processedLines = 0;
              const totalLines = lines.length;

              // تحديث التقدم: معالجة البيانات
              progressFill.style.width = '40%';
              progressText.textContent = 'معالجة البيانات...';

              lines.forEach((line) => {
                line = line.trim();
                if (!line) return;

                if (line.startsWith("رمز:")) {
                  // حفظ العملة السابقة إذا كانت موجودة
                  if (symbol && coinData) {
                    allCoinData[symbol] = coinData;
                    importedCoins++;

                    // تحديث التقدم أثناء المعالجة
                    const progress = 40 + (processedLines / totalLines) * 40;
                    progressFill.style.width = `${progress}%`;
                    progressText.textContent = `معالجة العملة ${importedCoins}: ${symbol}`;
                  }

                  symbol = line.split("رمز:")[1].trim();
                  coinData = getDefaultCoinDataStructure();
                  repIndex = 0;
                } else if (coinData) {
                  if (line.startsWith("سعر الدخول:")) {
                    coinData.initialEntryPrice = line.split(":")[1].trim();
                  } else if (line.startsWith("المبلغ:")) {
                    coinData.initialAmountDollars = line.split(":")[1].trim();
                  } else if (line.startsWith("تعزيز")) {
                    const parts = line.split(":")[1].split(",");
                    if (
                      parts.length === 2 &&
                      repIndex < coinData.repurchases.length
                    ) {
                      coinData.repurchases[repIndex].price = parts[0].trim();
                      coinData.repurchases[repIndex].amount = parts[1].trim();
                      repIndex++;
                    }
                  } else if (line.startsWith("هدف1:")) {
                    coinData.targets.tp1 = line.split(":")[1].trim();
                  } else if (line.startsWith("هدف2:")) {
                    coinData.targets.tp2 = line.split(":")[1].trim();
                  } else if (line.startsWith("هدف3:")) {
                    coinData.targets.tp3 = line.split(":")[1].trim();
                  } else if (line.startsWith("وقف:")) {
                    coinData.targets.sl = line.split(":")[1].trim();
                  }
                }
                processedLines++;
              });

              // حفظ العملة الأخيرة
              if (symbol && coinData) {
                allCoinData[symbol] = coinData;
                importedCoins++;
              }

              // تحديث التقدم: حفظ البيانات
              progressFill.style.width = '80%';
              progressText.textContent = 'حفظ البيانات...';

              // حفظ البيانات وتحديث الواجهة
              saveAllDataToLocalStorage();

              // تحديث التقدم: تحديث الواجهة
              setTimeout(() => {
                progressFill.style.width = '90%';
                progressText.textContent = 'تحديث الواجهة...';

                // تحديث جميع عناصر الواجهة
                updateCoinSelector();
                updateSummaryTable();
                updateCoinStatus();

                // تحديث التقدم: اكتمال
                progressFill.style.width = '100%';
                progressText.textContent = `تم استيراد ${importedCoins} عملة بنجاح!`;

                setTimeout(() => {
                  // إزالة مؤشر التقدم
                  document.body.removeChild(progressModal);

                  // إعادة تفعيل زر الاستيراد
                  if (importBtn) {
                    importBtn.disabled = false;
                    importBtn.innerHTML = '<i class="fas fa-file-import text-sm"></i><span class="text-sm font-medium">استيراد</span>';
                  }

                  showNotification(`تم استيراد ${importedCoins} عملة بنجاح. اختر عملة لعرض تفاصيلها.`, "success");

                  // إذا كانت هناك عملات، اختر الأولى تلقائياً وحدث البيانات
                  if (importedCoins > 0) {
                    const firstCoin = Object.keys(allCoinData)[0];
                    if (firstCoin) {
                      setTimeout(() => {
                        // تحديد العملة الأولى
                        coinSelector.value = firstCoin;
                        activeCoinSymbol = firstCoin;

                        // عرض بيانات العملة المحددة
                        displayCoinData(firstCoin);

                        // تحديث جدول DCA
                        loadDCAData();

                        // تحديث الحسابات
                        calculateActiveCoinDetails();

                        // تحديث جدول الملخص
                        updateSummaryTable();

                        // تحديث حالة العملة
                        updateCoinStatus();

                        // حفظ البيانات
                        saveAllDataToLocalStorage();
                      }, 300);
                    }
                  }
                }, 800);
              }, 200);

            } catch (error) {
              console.error("خطأ في استيراد البيانات:", error);

              // إزالة مؤشر التقدم في حالة الخطأ
              if (progressModal && document.body.contains(progressModal)) {
                document.body.removeChild(progressModal);
              }

              // إعادة تفعيل زر الاستيراد في حالة الخطأ
              if (importBtn) {
                importBtn.disabled = false;
                importBtn.innerHTML = '<i class="fas fa-file-import text-sm"></i><span class="text-sm font-medium">استيراد</span>';
              }

              showNotification("حدث خطأ أثناء استيراد البيانات. تأكد من صحة تنسيق الملف.", "error");
            }
          }, 50); // تأخير قصير لإظهار مؤشر التحميل
        };

        reader.onerror = function() {
          // إزالة مؤشر التقدم في حالة الخطأ
          if (progressModal && document.body.contains(progressModal)) {
            document.body.removeChild(progressModal);
          }

          // إعادة تفعيل زر الاستيراد في حالة الخطأ
          if (importBtn) {
            importBtn.disabled = false;
            importBtn.innerHTML = '<i class="fas fa-file-import text-sm"></i><span class="text-sm font-medium">استيراد</span>';
          }
          showNotification("فشل في قراءة الملف. حاول مرة أخرى.", "error");
        };

        reader.readAsText(file);

        // إعادة تعيين قيمة input file
        event.target.value = '';
      }

      // ===========================
      // DCA MANAGEMENT FUNCTIONS
      // ===========================

      // DCA Management Functions
      let dcaRows = [];
      let dcaRowCounter = 0;

      function addDCARow() {
        dcaRowCounter++;
        const newRow = {
          id: dcaRowCounter,
          dropPercent: '',
          dcaPrice: '',
          amount: '',
          quantity: 0,
          currentPnL: 0,
          targetPrice: '',
          expectedProfit: null,
          isSold: false
        };
        dcaRows.push(newRow);
        renderDCATable();
        saveDCAData();

        // Update all related displays
        updateRemainingQuantity();
        calculateActiveCoinDetails();
        updateSummaryTable();
        updateCoinStatus();
      }

      function deleteDCARow(rowId) {
        dcaRows = dcaRows.filter(row => row.id !== rowId);
        reorderDCARows(); // إعادة ترقيم الصفوف
        renderDCATable();
        saveDCAData();

        // Update all related displays
        updateRemainingQuantity();
        calculateActiveCoinDetails();
        updateSummaryTable();
        updateCoinStatus();
      }

      function clearAllDCA() {
        if (dcaRows.length === 0) {
          showNotification("لا توجد صفوف للحذف", "info");
          return;
        }

        showCustomConfirm(
          "هل أنت متأكد من حذف جميع صفوف التعزيز؟",
          function() {
            dcaRows = [];
            dcaRowCounter = 0; // إعادة تعيين العداد
            renderDCATable();
            saveDCAData();

            // Update all related displays
            updateRemainingQuantity();
            calculateActiveCoinDetails();
            updateSummaryTable();
            updateCoinStatus();

            showNotification("تم حذف جميع صفوف التعزيز", "success");
          }
        );
      }

      function sellDCARow(rowId) {
        const row = dcaRows.find(r => r.id === rowId);
        if (!row) return;

        showCustomConfirm(
          `هل أنت متأكد من بيع وحذف الصف رقم ${row.id}؟`,
          function() {
            // Remove the row from dcaRows array
            dcaRows = dcaRows.filter(r => r.id !== rowId);
            reorderDCARows(); // إعادة ترقيم الصفوف

            // Update DCA table
            renderDCATable();
            saveDCAData();

            // Update remaining quantity
            updateRemainingQuantity();

            // Update main calculations (portfolio summary)
            calculateActiveCoinDetails();

            // Update detailed portfolio table
            updateSummaryTable();

            // Update coin status
            updateCoinStatus();

            showNotification(`تم بيع وحذف الصف بنجاح`, "success");
          }
        );
      }

      // دالة إعادة ترقيم الصفوف
      function reorderDCARows() {
        dcaRows.forEach((row, index) => {
          row.id = index + 1;
        });
        // تحديث العداد ليكون آخر رقم مستخدم
        dcaRowCounter = dcaRows.length;
      }

      function updateRemainingQuantity() {
        if (!activeCoinSymbol || !allCoinData[activeCoinSymbol]) {
          const remainingDisplay = document.getElementById('remainingQuantityDisplay');
          if (remainingDisplay) {
            remainingDisplay.textContent = formatNumber(0, 8);
          }
          return 0;
        }

        const data = allCoinData[activeCoinSymbol];
        const initialEntryPrice = parseFloat(data.initialEntryPrice) || 0;
        const initialAmountDollars = parseFloat(data.initialAmountDollars) || 0;

        // Calculate initial quantity
        let totalQuantity = initialEntryPrice > 0 ? initialAmountDollars / initialEntryPrice : 0;

        // Add DCA quantities (only unsold ones)
        if (data.dcaRows) {
          data.dcaRows.forEach(row => {
            if (!row.isSold) {
              const dcaPrice = parseFloat(row.dcaPrice) || 0;
              const dcaAmount = parseFloat(row.amount) || 0;
              if (dcaPrice > 0 && dcaAmount > 0) {
                totalQuantity += dcaAmount / dcaPrice;
              }
            }
          });
        }

        // Add old repurchases system for backward compatibility
        if (data.repurchases) {
          data.repurchases.forEach((rp) => {
            const rpPrice = parseFloat(rp.price) || 0;
            const rpAmount = parseFloat(rp.amount) || 0;
            if (rpPrice > 0 && rpAmount > 0) {
              totalQuantity += rpAmount / rpPrice;
            }
          });
        }

        // Update display
        const remainingDisplay = document.getElementById('remainingQuantityDisplay');
        if (remainingDisplay) {
          remainingDisplay.textContent = formatNumber(totalQuantity, 8);
        }

        return totalQuantity;
      }

      function calculateDCAProfit(row) {
        const targetPrice = parseFloat(row.targetPrice) || 0;
        const dcaPrice = parseFloat(row.dcaPrice) || 0;
        const quantity = parseFloat(row.quantity) || 0;

        if (targetPrice <= 0 || dcaPrice <= 0 || quantity <= 0) {
          row.expectedProfit = null;
          return;
        }

        const profitAmount = (targetPrice - dcaPrice) * quantity;
        const profitPercent = ((targetPrice - dcaPrice) / dcaPrice) * 100;

        row.expectedProfit = {
          amount: profitAmount,
          percent: profitPercent
        };
      }

      function updateDCARow(rowId, field, value) {
        const row = dcaRows.find(r => r.id === rowId);
        if (!row) return;

        row[field] = value;

        // Auto-calculate quantity when price and amount are available
        if (field === 'dcaPrice' || field === 'amount') {
          const price = parseFloat(row.dcaPrice) || 0;
          const amount = parseFloat(row.amount) || 0;
          if (price > 0 && amount > 0) {
            row.quantity = amount / price;
          } else {
            row.quantity = 0;
          }
        }

        // Auto-calculate drop percentage when price is available
        if (field === 'dcaPrice' && activeCoinSymbol && allCoinData[activeCoinSymbol]) {
          const initialPrice = parseFloat(allCoinData[activeCoinSymbol].initialEntryPrice) || 0;
          const dcaPrice = parseFloat(value) || 0;
          if (initialPrice > 0 && dcaPrice > 0) {
            row.dropPercent = ((initialPrice - dcaPrice) / initialPrice * 100).toFixed(2);
          }
        }

        // Calculate current P&L
        const marketPrice = currentMarketPrices[activeCoinSymbol] || 0;
        const dcaPrice = parseFloat(row.dcaPrice) || 0;
        const quantity = parseFloat(row.quantity) || 0;

        if (marketPrice > 0 && dcaPrice > 0 && quantity > 0) {
          row.currentPnL = (marketPrice - dcaPrice) * quantity;
        } else {
          row.currentPnL = 0;
        }

        // Auto-calculate expected profit when target price is available
        calculateDCAProfit(row);

        renderDCATable();
        saveDCAData();
        updateRemainingQuantity();

        // Update main calculations to reflect DCA changes
        calculateActiveCoinDetails();

        // Update detailed portfolio table
        updateSummaryTable();

        // Update coin status
        updateCoinStatus();
      }

      function renderDCATable() {
        const tbody = document.getElementById('repurchaseRows');
        const emptyState = document.getElementById('dcaEmptyState');

        if (dcaRows.length === 0) {
          tbody.innerHTML = '';
          if (emptyState) emptyState.style.display = 'block';
          return;
        }

        if (emptyState) emptyState.style.display = 'none';

        tbody.innerHTML = dcaRows.map(row => `
          <tr class="hover:bg-slate-50 dark:hover:bg-slate-700 transition-all duration-200">
            <!-- رقم الصف -->
            <td class="px-2 py-3 text-center">
              <span class="inline-flex items-center justify-center w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 text-white text-xs font-bold rounded-full">
                ${row.id}
              </span>
            </td>

            <!-- نسبة الهبوط -->
            <td class="px-2 py-3 text-center">
              <input type="number"
                     value="${row.dropPercent}"
                     placeholder="%"
                     step="0.01"
                     class="dca-input"
                     readonly
                     title="يتم حساب هذه القيمة تلقائياً" />
            </td>

            <!-- سعر التعزيز -->
            <td class="px-2 py-3">
              <input type="number"
                     value="${row.dcaPrice}"
                     placeholder="السعر"
                     step="any"
                     class="dca-input"
                     onchange="updateDCARow(${row.id}, 'dcaPrice', this.value)" />
            </td>

            <!-- المبلغ -->
            <td class="px-2 py-3">
              <input type="number"
                     value="${row.amount}"
                     placeholder="المبلغ $"
                     step="any"
                     class="dca-input"
                     onchange="updateDCARow(${row.id}, 'amount', this.value)" />
            </td>

            <!-- الكمية -->
            <td class="px-2 py-3 text-center">
              <span class="font-mono text-xs font-semibold text-slate-700 dark:text-slate-300">
                ${formatNumber(row.quantity, 8)}
              </span>
            </td>

            <!-- الربح/الخسارة الحالية -->
            <td class="px-2 py-3 text-center">
              <span class="font-mono text-xs font-bold ${row.currentPnL > 0 ? 'profit-positive' : row.currentPnL < 0 ? 'profit-negative' : 'profit-neutral'}">
                $${formatNumber(row.currentPnL, 2)}
              </span>
            </td>

            <!-- سعر البيع المستهدف -->
            <td class="px-2 py-3">
              <input type="number"
                     value="${row.targetPrice}"
                     placeholder="سعر الهدف"
                     step="any"
                     class="dca-input"
                     onchange="updateDCARow(${row.id}, 'targetPrice', this.value)" />
            </td>

            <!-- الربح المتوقع -->
            <td class="px-2 py-3 text-center">
              ${row.expectedProfit ? `
                <div class="result-display ${row.expectedProfit.amount > 0 ? 'result-positive' : row.expectedProfit.amount < 0 ? 'result-negative' : 'result-neutral'}">
                  <div class="text-xs">
                    <div>$${formatNumber(row.expectedProfit.amount, 2)}</div>
                    <div>${formatNumber(row.expectedProfit.percent, 2)}%</div>
                  </div>
                </div>
              ` : `
                <div class="result-display result-neutral">
                  <span class="text-xs">---</span>
                </div>
              `}
            </td>

            <!-- إجراءات -->
            <td class="px-2 py-3 text-center">
              <button onclick="sellDCARow(${row.id})"
                      class="dca-btn dca-btn-delete"
                      title="بيع وحذف الصف">
                <i class="fas fa-hand-holding-usd text-xs"></i>
              </button>
            </td>
          </tr>
        `).join('');
      }

      function saveDCAData() {
        if (!activeCoinSymbol) return;

        if (!allCoinData[activeCoinSymbol]) {
          allCoinData[activeCoinSymbol] = getDefaultCoinDataStructure();
        }

        allCoinData[activeCoinSymbol].dcaRows = dcaRows;
        allCoinData[activeCoinSymbol].dcaRowCounter = dcaRowCounter;
        saveAllDataToLocalStorage();
      }

      function loadDCAData() {
        if (!activeCoinSymbol || !allCoinData[activeCoinSymbol]) {
          dcaRows = [];
          dcaRowCounter = 0;
          renderDCATable();
          return;
        }

        const coinData = allCoinData[activeCoinSymbol];
        dcaRows = coinData.dcaRows || [];
        dcaRowCounter = coinData.dcaRowCounter || 0;

        // إعادة ترقيم الصفوف للتأكد من التسلسل الصحيح
        if (dcaRows.length > 0) {
          reorderDCARows();
        }

        renderDCATable();
      }

      // Make functions globally available
      window.addDCARow = addDCARow;
      window.deleteDCARow = deleteDCARow;
      window.clearAllDCA = clearAllDCA;
      window.sellDCARow = sellDCARow;
      window.updateDCARow = updateDCARow;
      window.updateRemainingQuantity = updateRemainingQuantity;
      window.reorderDCARows = reorderDCARows;
      window.openNotificationsWithCoin = openNotificationsWithCoin;


